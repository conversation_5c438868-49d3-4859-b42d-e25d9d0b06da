require 'sidekiq/web'
require 'sidekiq-scheduler/web'

Rails.application.routes.draw do
  devise_for :patients, controllers: { passwords: 'patients/passwords', sessions: 'patients/sessions', registrations: 'patients/registrations' }
  devise_for :users, controllers: { sessions: 'users/sessions', passwords: 'users/passwords' }

  get "up" => "rails/health#show", as: :rails_health_check
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  devise_scope :user do
    get '/users/password/lab_user_success', to: 'users/passwords#lab_user_success', as: :lab_user_password_success
  end

  Sidekiq::Web.use(Rack::Auth::Basic) do |username, password|
    username == ENV['SIDEKIQ_USERNAME'] && password == ENV['SIDEKIQ_PASSWORD']
  end
  mount Sidekiq::Web => '/sidekiq'

  mount LetterOpenerWeb::Engine, at: "/letter_opener" if Rails.env.development?

  match "/404", to: "errors#not_found", via: :all
  match "/500", to: "errors#internal_server_error", via: :all

  namespace :webhooks do
    resources :sinch, only: [] do
      collection do
        post :incoming_messages
        post :read_receipts
        post :incoming_mail
      end
    end
  end

  resources :stripe_webhooks

  # QR Code device registration (outside admin namespace for public access)
  get 'qr_register_device/:practice_id/:token', to: 'device_registration#qr_register', as: 'qr_register_device'
  get 'device_registration_success', to: 'device_registration#registration_success', as: 'device_registration_success'
  get 'device_waiting', to: 'device_registration#waiting', as: 'device_waiting'

  namespace :admin do
    # Device registration endpoints
    post 'device_registration/register_current_device', to: 'device_registration#register_current_device'
    get 'device_registration/check_device_registration', to: 'device_registration#check_device_registration'
    post 'device_registration/ping', to: 'device_registration#ping'
    post 'device_registration/pong', to: 'device_registration#pong'

    get 'calendar_sidebar', to: 'calendar#sidebar'
    get 'inner_calendar_sidebar', to: 'calendar#inner_sidebar'
    resources :dashboards

    resources :dashboard_widgets do
      collection do
        patch :update_positions
      end
    end

    post "pusher/authenticate" => "pusher#authenticate"

    resources :sessions, only: :create

    resources :notifications, only: [] do
      post :handle_action, on: :member
      post :clear_all, on: :collection
      get :index, on: :collection
      get :check_scheduled, on: :collection
    end

    namespace :crm do
      root to: 'crm#index'
      resources :boards do
        resources :lists, only: [:create, :update, :destroy, :show] do
          get :table, on: :member
          resources :cards, only: [:create, :update, :destroy] do
            post :move, on: :member
          end
          post :move, on: :member
        end
        get :lists, to: 'lists#index_for_board', on: :member
        resources :labels, only: [:index, :create, :update, :destroy]
        resources :custom_fields, only: [:index, :show, :create, :update, :destroy]
        resources :completion_zones, only: [:create, :update, :destroy] do
          get :count, on: :member
          get :cards, on: :member
        end

        resources :checklists do
          get :board_checklists, on: :collection
          resources :checklist_items, only: [:create, :update, :destroy] do
            patch :toggle, on: :member
          end
        end
        get 'completed_cards', to: 'cards#completed'
        get 'archived_cards', to: 'cards#archived'
        get 'completed_cards/count', to: 'boards#completed_cards_count'
        get 'archived_cards/count', to: 'boards#archived_cards_count'
        resources :cards, only: [:create, :update, :destroy] do
          get :restrictions
          post :move, on: :member
        end
      end

      resources :lists, only: [] do
        resources :cards, only: [:create, :update, :destroy]
        post :move, on: :member
        get :practice_id, on: :member
        patch :update_title, on: :member
        get :card_count, on: :member
      end

      resources :cards, only: [:index] do
        post :move_to_board, on: :member
        post :move, on: :member
        post :update_status, on: :member
        post :restore, on: :member
        patch :archive, on: :member
        post :add_checklist, on: :member
        delete :remove_checklist, on: :member
        get :edit, on: :member
        get :show, on: :member
        get :conversation, to: 'conversations#show', on: :member
        get :assets, to: 'assets#show', on: :member
        get :payments, to: 'payments#show', on: :member
        get :appointments, to: 'appointments#show', on: :member
        get :actions, to: 'actions#show', on: :member
        get :directions, to: 'directions#show', on: :member
        post :directions_pdf, to: 'directions#generate_pdf', on: :member
        get :labels, on: :member
        patch :update_last_contacted, on: :member
        patch :update_description, on: :member
        patch 'checklist_items/:id/toggle', to: 'checklist_items#toggle', as: 'toggle_checklist_item'
        resources :activities, only: [:index]
        resources :comments, only: [:create, :update, :destroy], defaults: { format: :json }
        resource :card_labels, only: [:update]
        resource :checklist, only: [:show, :create, :update, :destroy] do
          resources :checklist_items, only: [:create, :update, :destroy] do
            patch :toggle, on: :member
          end
        end
        get 'members', to: 'card_members#index'
        patch 'update_members', to: 'card_members#update'
        patch 'custom_field_values/:custom_field_id', to: 'custom_field_values#update', as: 'custom_field_value'
      end



      resources :boards, only: [] do
        get :lists, on: :member
      end

      resources :patients, only: [] do
        collection do
          get :search
        end
      end

      resources :treatments, only: [:index]
      resources :board_members, only: [:index]
    end

    namespace :privacy_screen do
      post :send_verification
      post :verify_code
      post :verify_pin
      post :verify_password
      get :user_mfa_method
      get :user_settings
    end

    resources :conversations do
      collection do
        get :search_patients
      end
      post :merge_conversation, on: :collection
      member do
        get :select2
        get :messages
        post :create_message
        post :mark_as_read
        post :mark_as_unread
        post :update_label
        post :update_name
        post :insert_whatsapp_template
        post :insert_dynamic_template
        post :insert_letter_template
        patch :toggle_flag
        patch :toggle_archived
        get :message_content
        get :notification_templates
      end
    end

    resources :general_settings, only: :index
    resources :patients do
      collection do
        get :search
        get :select2_search
        get :bypass_2fa
        post :create_temporary
        post :create_temporary_with_practice
      end
      get :linked_conversation, on: :member
      post :download_assets, on: :member
      get :notes, on: :member
      post :login_as_patient, on: :member
      get :available_devices, on: :member
      resources :registered_devices
      get :assets, on: :member
      get :actions, on: :member
      get :prescriptions, on: :member
      member do
        post :update_asset_labels
        post :archive_assets
      end
      post :forgot_password, on: :member
      post :reset_password, on: :member
      post :update_field, on: :member
      post :add_payment_plan, on: :member
      delete :remove_payment_plan, on: :member
      patch :update_assigned_staff, on: :member
      patch :update_assigned_practices, on: :member
      post :remove_team_member, on: :member
      get :linked_family, on: :member
      post :update_linked_family, on: :member
      get :search, on: :collection
      post :toggle_archive, on: :member
      patch :archive, on: :member
      get :action_banner_status, on: :member

      resources :recall_schedules, only: %i[create update destroy], controller: 'patients/recall_schedules'
      resources :recalls, only: %i[index edit create update destroy], controller: 'patients/recalls' do
        post :send_reminder, on: :member
      end

      resources :payment_plans, controller: 'patients/payment_plans' do
        get :add_payment_method, on: :member
        post :save_payment_method, on: :member
      end

      resource :account, controller: 'patients/account' do
        get :statement, on: :collection
      end

      resources :letters, only: %i[index], controller: 'patients/letters'

      resource :charting, only: :show, controller: 'patients/charting' do
        get :load_course_of_treatment, on: :collection
        get :load_course_of_treatment_form_data, on: :collection
        get :load_course_of_treatment_details, on: :collection
        get :load_charted_treatments, on: :collection
        get :load_tooth_history, on: :collection
        get :load_calendar_bookings, on: :collection
        delete :reset_tooth, on: :collection
        patch :reorder_charted_treatments, on: :collection
        patch :reorder_appointments, on: :collection
        post :create_appointment, on: :collection
        post :create_course_of_treatment, on: :collection
        post :create_treatment, on: :collection
        delete :delete_treatment, on: :collection
        patch :update_charted_treatment, on: :collection
        post :assign_base_treatment, on: :collection
        get :load_base_treatments, on: :collection
        get :load_history_treatments, on: :collection
        post :create_charting_appointment_note, on: :collection
        get :edit_appointment_note, on: :collection
        post :update_appointment_note, on: :collection
        delete :delete_appointment_note, on: :collection
        delete :delete_appointment, on: :collection
        patch :update_patient_payment_plan, on: :collection
        get :get_users_list, on: :collection
        patch :change_dentist, on: :collection
        patch :change_cot, on: :collection
        post :upload_photo, on: :collection
        post :upload_xray, on: :collection
        post :upload_opg, on: :collection
        post :upload_cbct, on: :collection
        post :upload_stl, on: :collection
        post :complete_course_of_treatment, on: :collection
        post :update_cot_name, on: :collection
        post :accept_cot, on: :collection
        post :archive_cot, on: :collection
        post :activate_cot, on: :collection
        post :copy_cot, on: :collection
        post :assign_patient_treatment, on: :collection
        get :find_treatment_by_name, on: :collection
        post :change_date_and_price, on: :collection
        post :charge_cot, on: :collection
        post :save_bpe_and_bewe, on: :collection
        get :nhs_medications, on: :collection
        get :nhs_medication, on: :collection
        get :diagnocat, on: :collection
        post :create_appointments, on: :collection
        post :lock_appointment, on: :collection
        post :unlock_appointment, on: :collection
        post :lock_appointment_note, on: :collection
        post :unlock_appointment_note, on: :collection
        patch :associate_calendar_booking, on: :collection
        get :load_all_modals, on: :collection
        post :favorite_treatment, on: :collection
      end

      resources :perio_exams, controller: 'patients/perio_exams' do
        get :compare, on: :collection
        get :plaque_and_bleeding, on: :member
      end

      member do
        get :conversation
        get :actions
        get :assets
        get :prescriptions
        get :treatment_plans_data
        get :forgot_password
        post :reset_password
        patch :update_teethgrid_view
      end

      resources :invoices, controller: 'patients/invoices' do
        get :pdf, on: :member
        get :sms_link, on: :member
        get :email_link, on: :member
        get :payments_summary, on: :member
        patch :cancel, on: :member
      end
      resources :payments, controller: 'patients/payments' do
        post :link_to_payment, on: :collection
        post :link_to_invoice, on: :collection
        post :edit_reason, on: :collection
        post :refund, on: :member
      end
      resources :event_logs, controller: 'patients/event_logs'
      resources :charting_appointments, controller: 'patients/charting_appointments', only: :index
      resource :lab_works, only: :show, controller: 'patients/lab_works'

      resources :medical_histories, controller: 'patients/medical_histories' do
        get :send_to_email, on: :collection
        get :send_to_sms, on: :collection
        get :pdf, on: :member

        member do
          get :card_summary_partial
          get :card_alerts_partial
          get :card_changes_partial
          get :modal_data
          post :add_alert
          post :remove_alert
        end
      end
    end

    resources :lab_works do
      get :table_view, on: :collection
      post :update_status, on: :member
      post :upload_invoice, on: :member
    end

    resources :medications, only: [] do
      collection do
        get :search
      end
    end

    resources :prescriptions do
      get :email, on: :member
      get :download_pdf, on: :member
      get :review, on: :member
      post :finalize, on: :member
      post :add_signature, on: :member
      post :verify_auth, on: :collection
    end

    resources :lab_work_files, only: [] do
      patch :update_invoice, on: :member
      delete :delete_invoice, on: :member
    end

    resources :lab_dockets do
      get :create_pdf, on: :member
    end

    resources :patient_assets, only: [:create, :destroy] do
      post :update_labels, on: :collection
      post :delete_multiple, on: :collection

      member do
        post :rename
      end
    end

    resources :patient_notes do
      member do
        get :edit
        get :refresh
      end
    end

    resources :reports, only: :index do
      member do
        post :favourite
      end

      collection do
        get :practice_bookings
        get :treatment_takings
        get :treatment_plan_revenue
        get :clinician_invoices
        get :scheduled_payments
        get :patient_outcomes
        get :takings
        get :patient_accounts
        get :invoices
        get :patients
        get :new_patients_per_month
        get :course_of_treatments
        get :patients_by_age
        get :lapsed_patients
        get :active_patients_per_practitioner
        get :radiographs
        get :prescriptions
        get :appointments
        get :appointments_by_month
      end
    end

    resources :treatment_plans, only: [:index, :update] do
      get :table_view, on: :collection
      patch :reset, on: :member
      get :send_to_view, on: :member
      get :users_for_board, on: :collection
    end
    get 'treatment_plan_options/option/select2_search', to: 'treatment_plan_options/option#select2_search'
    resources :treatment_plan_options do
      post :create_summary, on: :collection
      get :pdf_url, on: :member
      patch :decline, on: :member
      patch :accept, on: :member
      patch :import_cot, on: :member
      resources :option, controller: 'treatment_plan_options/option'
    end

    resources :actions do
      member do
        post :mark_as_completed
        post :complete
        get :timeline
        post :set_reminder
      end
    end

    resources :action_comments, only: [:create, :index]

    resources :ai_tools, only: [] do
      post :improve_text, on: :collection
    end

    resources :nhs_udas


    resources :event_logs, only: :index

    namespace :general_settings do
      resources :onboarding_forms, except: %i[show destroy]
      resources :hr_templates, only: %i[index edit create update new]
      resources :users do
        patch :archive, on: :member
        patch :unarchive, on: :member
        collection do
          get :search
        end
        get :search, on: :collection
        patch :update_treatment_plans_view, on: :member
        get :select2_search, on: :collection
      end
      resources :roles
      resources :medical_history_questions do
        resources :medical_history_answers, only: :create
        post :set_position, on: :collection
      end

      resources :medical_history_answers, only: [:update, :destroy] do
        resources :medical_history_questions, only: :create
      end
      resources :practices do
        post :send_verification_code, on: :member
        post :disable_two_factor, on: :member
        resources :registered_devices, controller: 'practices/registered_devices' do
          post :toggle_active, on: :member
          patch :rename, on: :member
          post :force_logout, on: :member
          get :generate_qr_code, on: :collection
        end
        resource :information, controller: 'practices/information', only: %i[edit update]
        resource :opening_hours, controller: 'practices/opening_hours', only: %i[edit update]
        resource :payment_processing, controller: 'practices/payment_processing', only: %i[edit update]
        resource :communications, controller: 'practices/communication', only: %i[edit update]
        resource :whatsapp_templates, controller: 'practices/whatsapp_templates', only: %i[edit update]
      end
      resources :labs do
        get :for_board, on: :collection
      end
      resources :treatment_categories do
        post :reorder, on: :member
      end
      resources :treatments do
        get :select2_search, on: :collection
      end
      resources :automations do
        patch :toggle_enabled, on: :member
      end
      resource :stripe, controller: 'stripe' do
        get :connect, on: :collection
        get :onboarding, on: :collection
        get :client_secret, on: :collection
      end
      resource :calendar_setting, only: %i[show update] do
        post :generate_description_ai, on: :collection
      end
      resources :treatment_plan_estimates, only: :index do
        resources :treatment_plan_templates, controller: 'treatment_plan_estimates/treatment_plan_templates'
        resources :informations, controller: 'treatment_plan_estimates/informations'
        resources :testimonials, controller: 'treatment_plan_estimates/testimonials'
        resource :practice_info, controller: 'treatment_plan_estimates/practice_info', only: %i[edit update]
      end
      resources :document_templates do
        get 'preview', on: :member
      end
      resources :cot_templates
      resources :notification_templates
      resources :patient_asset_labels
      resources :alerts, except: :show
      resources :patient_gps, except: :show do
        collection do
          get :search
        end
      end
      resources :cot_payment_plans, except: [:show, :edit]
      resources :cot_template_notes do
        member do
          patch :convert_to_user_note
          patch :convert_to_practice_note
        end
      end
      resources :cot_categories, except: [:show] do
        get :for_board, on: :collection
      end
      resources :medications, except: [:show, :edit]
    end

    namespace :hr_management do
      resources :announcement_reactions, only: %i[show create destroy]
      resources :announcements, except: %i[new] do
        post :publish
      end
      get "dashboard", to: "dashboards#index"
      resources :group_meetings, except: %i[new edit] do
        member do
          get :join
          post :end
        end
      end
      resources :form_assignments, only: %i[index show create] do
        member do
          get :fill
          get :complete
          post :submit
          post :submit_filled
        end
        collection do
          post :manual_upload
        end
      end
      resources :holidays, except: %i[show] do
        post :approve
        post :reject
      end
      resources :meetings, only: %i[index show new create] do
        member do
          get :dismiss
          get :join
          get :notification
          post :comment
        end
      end
      resources :shifts, except: %i[show]
      resources :time_sheets, only: [:index]
      resources :users, only: %i[index show]
    end

    resources :communication_accounts do
      member do
        put :set_default
        put :update_users
        get :users
        put :activate
      end
    end

    resources :whatsapp_templates do
      member do
        get :submit, as: :submit
      end
    end

    resources :calendar_day_notes, only: %i[create update destroy]

    resources :calendar_bookings, only: %i[create update] do
      resources :notes, controller: 'calendar_booking_notes', only: [:index, :create, :destroy]

      collection do
        get :staff_calendar
        get :staff_calendar_pdf
        get :staff_cancellation_list
        get :slots

        get :slot_finder_offcanvas
        get :drag_offcanvas

        get :search
        get :cancellation_list
        post :offer_slot_to_patients
      end

      member do
        get :reschedule_offcanvas
        get :modal
        get :edit_times_modal

        get :cancellation_list_modal
        get :edit_cancellation_list_criteria
        post :update_cancellation_list_criteria
        post :remove_from_cancellation_list

        post :move
        post :cancel
        post :update_status
        post :update_details
        post :update_payment_request
      end

      resources :calendar_booking_notes, only: [:create, :destroy], path: 'notes'
    end

    namespace :calendar_bookings do
      resources :charting_appointments, only: %i[] do
        collection do
          get :upcoming
        end
      end
      resources :recalls, only: %i[] do
        collection do
          get :upcoming
        end
      end

      resources :course_of_treatments, only: %i[] do
        collection do
          get :for_select
        end
      end
    end

    resources :calendar_booking_cancellations, only: %i[] do
      member do
        get :accept
      end
    end

    root 'calendar_bookings#staff_calendar'

    resources :signature_requests, only: [:new, :create, :update, :index, :show, :edit] do
      member do
        post :resend_via_email
        post :resend_via_sms
        patch :update_status
      end
      get :fetch_patient_appointments, on: :collection
      get :table_view, on: :collection
      post :get_document_preview, on: :collection
      post :pdf_preview, on: :collection
    end
    resources :signable_documents, only: [:new, :create]

    resources :patient_notes, only: :create do
      member do
        patch :change_color
        patch :archive
        patch :pin
        get :refresh
      end
    end
  end

  namespace :patients do
    get 'already_signed', to: 'signature_requests#already_signed'
    get 'enter_password', to: 'verification#enter_password'
    get 'set_password', to: 'verification#set_password'
    get 'signature_complete', to: 'signature_requests#signature_complete'
    get 'verify_options', to: 'verification#options'
    post 'save_password', to: 'verification#save_password'
    post 'verify_sms', to: 'verification#verify_sms'
    post 'verify_email', to: 'verification#verify_email'
    post 'verify_staff', to: 'verification#verify_staff'
    post 'verify_identity', to: 'verification#verify_identity'
    post 'verify_password', to: 'verification#verify_password'

    resource :two_factor, controller: :two_factor, only: [:select_method, :verify] do
      root to: 'two_factor#select_method'
      post :verify
    end

    resources :medical_histories do
      get :submitted, on: :collection
    end

    resources :calendar_bookings do
      get :confirm, on: :member
      get :decline, on: :member
    end

    resources :patients, only: %i[edit update] do
      get :dashboard, on: :collection
      get :medical_histories, on: :collection
      get :treatment_plans, on: :collection
      get :signature_requests, on: :collection
      get :documents, on: :collection
      patch :update_password, on: :collection
      post :send_sms, on: :collection
      patch :update_field, on: :member
    end

    namespace :online_bookings do
      get 'success', to: 'results#success'
      get 'download_file', to: 'results#download_file'
      resources :bookings, only: %i[create]
      resources :calendars, only: %i[new]
      resources :dentists, only: %i[index]
      resources :patients, only: %i[index new]
      resources :payments, only: %i[new]
      resources :practices, only: %i[index]
      resources :my_appointments, only: %i[index]
      resources :reserved_blocks, only: %i[index]
    end

    resources :treatment_plans, only: :show do
      get :accept, on: :member
      get :view, on: :member
      post :sign, on: :member
      get :treatment_plan_options, on: :collection
      get :download_pdf, on: :member
      get :complete, on: :member
    end

    resources :signature_requests, param: :token, only: [:show] do
      member do
        post :sign, as: 'submit'
        get :complete, as: 'complete'
        get :document_content, as: 'document_content'
        get :sign_document, as: 'sign_document'
      end
    end
  end

  resources :invoices do
    get :pdf, on: :member
    get :pay, on: :member
  end

  root 'admin/calendar_bookings#staff_calendar'
end
