# frozen_string_literal: true

module Admin
  module PatientAssetsHelper
    DEFAULT_LABELS = ['Clinical Photographs', 'X-Rays', 'OPGs', 'CBCTs', '3D STL Files', 'Consent Forms',
                      'Treatment Plan', 'Prescription', 'Medical History', 'Invoices', 'Documents', 'Lab Dockets'].freeze

    def asset_icon(asset)
      if asset.file.attached? && asset.file.content_type&.start_with?('image/')
        image_tag(asset.file.url, class: 'asset-thumbnail')
      else
        image_tag('icons/asset.svg', class: 'asset-icon')
      end
    end

    def assets_nav_icon(label)
      case label
      when 'Clinical Photographs'
        'fa-light fa-camera'
      when 'X-Rays'
        'fa-light fa-teeth'
      when 'OPGs'
        'fa-light fa-panorama'
      when 'CBCTs'
        'fa-light fa-cube'
      when '3D STL Files'
        'fa-light fa-cubes'
      when 'Consent Forms'
        'fa-light fa-file-signature'
      when 'Treatment Plan'
        'fa-light fa-clipboard-list'
      when 'Prescription'
        'fa-light fa-prescription-bottle'
      when 'Medical History'
        'fa-light fa-file-medical'
      when 'Invoices'
        'fa-light fa-file-invoice'
      when 'Documents'
        'fa-light fa-file-lines'
      else
        'fa-light fa-list'
      end
    end

    def assets_nav_color_class(label)
      case label
      when 'Clinical Photographs'
        'dusty-pink-bg'
      when 'X-Rays'
        'light-blue-bg'
      when 'OPGs'
        'complimentary-teal-bg'
      when 'CBCTs'
        'light-purple-bg'
      when '3D STL Files'
        'light-green-bg'
      when 'Consent Forms'
        'peach-orange-bg'
      when 'Treatment Plan'
        'yellow-bg'
      when 'Prescription'
        'light-purple-bg'
      when 'Medical History'
        'light-green-bg'
      when 'Invoices'
        'dark-pink-bg'
      when 'Documents'
        'gray-bg'
      when 'Lab Dockets'
        'light-blue-bg'
      else
        'peach-orange-bg'
      end
    end

    # Helper method to get badge class for a label
    def asset_badge_class(label)
      # For multiple labels, use the first one
      first_label = label.to_s.split(',').first&.strip
      assets_nav_color_class(first_label)
    end

    def assets_nav
      render 'admin/shared/vertical_nav', links: [
        {
          label: 'All',
          icon: 'fa-light fa-list',
          color_class: 'peach-orange-bg',
          active: true,
          options: { data: { "label": 'all' } }
        },
        *DEFAULT_LABELS.map do |label|
          {
            label: label,
            icon: assets_nav_icon(label),
            color_class: assets_nav_color_class(label),
            options: { data: { "label": label } }
          }
        end
      ]
    end
  end
end
