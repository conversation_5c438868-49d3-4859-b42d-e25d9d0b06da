# frozen_string_literal: true

module AssetTable<PERSON>elper
  def assets_table_columns
    [
      {
        label: '<input type="checkbox" id="assets_select_all" class="asset-checkbox-all">'.html_safe,
        content: lambda { |asset|
          if asset_is_image?(asset)
            # For images, use a combined checkbox with both classes and all data attributes
            content_tag(:input, '',
                        type: 'checkbox',
                        class: 'asset-checkbox image-selection-checkbox w-4 h-4 text-blue-600 border-gray-300 rounded ' \
                               'focus:ring-blue-500 focus:ring-2',
                        data: {
                          asset_id: asset.id,
                          image_url: rails_blob_path(asset.file, disposition: 'inline'),
                          filename: asset.file.filename.to_s,
                          content_type: asset.file.content_type
                        })
          else
            # For non-images, use regular asset checkbox
            content_tag(:input, '', type: 'checkbox', class: 'asset-checkbox', data: { asset_id: asset.id })
          end
        },
        class: 'w-8'
      },
      { label: 'Name', content: ->(asset) { asset_name_column(asset) }, class: 'w-40' },
      { label: 'Label', content: ->(asset) { asset_label_column(asset) }, class: 'w-40' },
      {
        label: 'Uploaded',
        content: ->(asset) { asset.created_at.strftime('%d/%m/%Y') },
        class: 'w-24 text-sm text-gray-500'
      },
      {
        label: 'Size',
        content: ->(asset) { asset.file.attached? ? number_to_human_size(asset.file.byte_size) : 'N/A' },
        class: 'w-20 text-left text-sm text-gray-500'
      }
    ]
  end

  private

  def asset_name_column(asset)
    icon_class = asset_icon_class(asset.label)

    content_tag(:div, class: 'flex items-center') do
      content_tag(:div, class: 'flex-shrink-0 mr-3') do
        content_tag(:i, '', class: "#{icon_class} h-5 w-5")
      end +
        content_tag(:div, class: 'flex flex-col min-w-0') do
          if asset.file.attached?
            content_tag(:span, asset.file.filename.to_s, class: 'text-sm font-medium text-gray-900 truncate') +
              content_tag(:span, "#{number_to_human_size(asset.file.byte_size)} • #{asset.file.content_type}",
                          class: 'text-xs text-gray-500')
          else
            asset_name = case asset.label
                         when 'Prescription'
                           "Prescription ##{asset.assignable_id}"
                         when 'Medical History'
                           "Medical History ##{asset.medical_history_id}"
                         else
                           'No file attached'
                         end
            content_tag(:span, asset_name, class: 'text-sm font-medium text-gray-900 truncate') +
              content_tag(:span, asset.label, class: 'text-xs text-gray-500')
          end
        end
    end
  end

  def asset_label_column(asset)
    color_class = asset_label_color_class(asset.label)
    content_tag(:span, asset.label, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{color_class}")
  end

  def file_icon_html(asset)
    return default_file_icon unless asset.file.attached?

    case asset.file.content_type
    when %r{^image/}
      image_file_icon
    when 'application/pdf'
      pdf_file_icon
    when %r{^video/}
      video_file_icon
    else
      default_file_icon
    end
  end

  def image_file_icon
    content_tag(:div, class: 'w-8 h-8 bg-green-100 rounded flex items-center justify-center') do
      content_tag(:svg, class: 'w-4 h-4 text-green-600', fill: 'currentColor', viewBox: '0 0 20 20') do
        content_tag(:path, '', 'fill-rule': 'evenodd',
                               d: 'M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z',
                               'clip-rule': 'evenodd')
      end
    end
  end

  def pdf_file_icon
    content_tag(:div, class: 'w-8 h-8 bg-red-100 rounded flex items-center justify-center') do
      content_tag(:svg, class: 'w-4 h-4 text-red-600', fill: 'currentColor', viewBox: '0 0 20 20') do
        content_tag(:path, '', 'fill-rule': 'evenodd',
                               d: 'M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z',
                               'clip-rule': 'evenodd')
      end
    end
  end

  def video_file_icon
    content_tag(:div, class: 'w-8 h-8 bg-purple-100 rounded flex items-center justify-center') do
      content_tag(:svg, class: 'w-4 h-4 text-purple-600', fill: 'currentColor', viewBox: '0 0 20 20') do
        content_tag(:path, '', 'fill-rule': 'evenodd',
                               d: 'M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6z' \
                                  'M14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z',
                               'clip-rule': 'evenodd')
      end
    end
  end

  def default_file_icon
    content_tag(:div, class: 'w-8 h-8 bg-gray-100 rounded flex items-center justify-center') do
      content_tag(:svg, class: 'w-4 h-4 text-gray-600', fill: 'currentColor', viewBox: '0 0 20 20') do
        content_tag(:path, '', 'fill-rule': 'evenodd',
                               d: 'M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z',
                               'clip-rule': 'evenodd')
      end
    end
  end

  def asset_icon_class(label)
    case label
    when 'Documents'
      'fa-light fa-file-lines text-blue-500'
    when 'X-Rays'
      'fa-light fa-x-ray text-purple-500'
    when 'Clinical Photographs'
      'fa-light fa-image text-amber-500'
    when 'Invoices'
      'fa-light fa-file-invoice text-orange-500'
    when 'Treatment Plan'
      'fa-light fa-list-check text-teal-500'
    when 'Prescription'
      'fa-light fa-prescription-bottle text-violet-500'
    when 'Medical History'
      'fa-light fa-file-medical text-emerald-500'
    when 'Consent Forms'
      'fa-light fa-signature text-green-500'
    when '3D STL Files'
      'fa-light fa-cube text-red-500'
    when 'OPGs'
      'fa-light fa-teeth text-indigo-500'
    when 'CBCTs'
      'fa-light fa-head-side-medical text-pink-500'
    when 'Lab Dockets'
      'fa-light fa-flask text-cyan-500'
    else
      'fa-light fa-file text-gray-500'
    end
  end

  def asset_label_color_class(label)
    case label
    when 'Documents'
      'bg-blue-100 text-blue-800'
    when 'X-Rays'
      'bg-purple-100 text-purple-800'
    when 'Clinical Photographs'
      'bg-amber-100 text-amber-800'
    when 'Invoices'
      'bg-orange-100 text-orange-800'
    when 'Treatment Plan'
      'bg-teal-100 text-teal-800'
    when 'Prescription'
      'bg-violet-100 text-violet-800'
    when 'Medical History'
      'bg-emerald-100 text-emerald-800'
    when 'Consent Forms'
      'bg-green-100 text-green-800'
    when '3D STL Files'
      'bg-red-100 text-red-800'
    when 'OPGs'
      'bg-indigo-100 text-indigo-800'
    when 'CBCTs'
      'bg-pink-100 text-pink-800'
    when 'Lab Dockets'
      'bg-cyan-100 text-cyan-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def asset_file_buttons(asset)
    if asset.file.attached?
      download_button = link_to(rails_blob_path(asset.file, disposition: 'attachment'),
                                class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-blue-700 ' \
                                       'bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors opacity-0 ' \
                                       'group-hover:opacity-100 transition-opacity duration-200') do
        concat(render_icon('download'))
        concat(' Download')
      end
      view_button = view_action_button(rails_blob_path(asset.file, disposition: 'inline'))
      download_button + view_button
    else
      content_tag(:span, 'No file attached',
                  class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-gray-500 ' \
                         'bg-gray-100 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200')
    end
  end

  def asset_archive_button(asset)
    if asset.archived?
      content_tag(:button,
                  class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-green-700 ' \
                         'bg-green-100 rounded-lg hover:bg-green-200 transition-colors opacity-0 ' \
                         'group-hover:opacity-100 transition-opacity duration-200 archive-asset-btn',
                  data: { asset_id: asset.id, archived: 'true' }) do
        concat(render_icon('archive-restore'))
        concat(' Restore')
      end
    else
      content_tag(:button,
                  class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-gray-700 ' \
                         'bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors opacity-0 ' \
                         'group-hover:opacity-100 transition-opacity duration-200 archive-asset-btn',
                  data: { asset_id: asset.id, archived: 'false' }) do
        concat(render_icon('archive'))
        concat(' Archive')
      end
    end
  end

  def asset_action_buttons(asset)
    content_tag(:div, class: 'flex items-center justify-end gap-2 group') do
      if asset.file.attached?
        concat(asset_preview_button(asset)) if asset_is_image?(asset)
        concat(asset_download_button(asset))
        concat(view_action_button(rails_blob_path(asset.file, disposition: 'inline')))
      elsif asset.label == 'Prescription' && asset.assignable_type == 'Prescription'
        concat(prescription_view_button(asset))
      elsif asset.label == 'Medical History' && asset.medical_history_id.present?
        concat(medical_history_view_button(asset))
      else
        concat(asset_no_file_message)
      end
      concat(asset_archive_button(asset))
    end
  end

  def asset_is_image?(asset)
    return false unless asset.file.attached?

    asset.file.content_type&.start_with?('image/')
  end

  def asset_preview_button(asset)
    content_tag(:button,
                class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-purple-700 ' \
                       'bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors opacity-0 ' \
                       'group-hover:opacity-100 transition-opacity duration-200 image-preview-btn',
                data: {
                  asset_id: asset.id,
                  image_url: rails_blob_path(asset.file, disposition: 'inline'),
                  filename: asset.file.filename.to_s
                }) do
      concat(content_tag(:i, '', class: 'fa-light fa-eye'))
      concat(' Preview')
    end
  end

  def asset_download_button(asset)
    link_to(rails_blob_path(asset.file, disposition: 'attachment'),
            class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-blue-700 ' \
                   'bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors opacity-0 ' \
                   'group-hover:opacity-100 transition-opacity duration-200') do
      concat(render_icon('download'))
      concat(' Download')
    end
  end

  def asset_no_file_message
    content_tag(:span, 'No file attached',
                class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-gray-500 ' \
                       'bg-gray-100 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200')
  end

  def prescription_view_button(asset)
    link_to(admin_prescription_path(asset.assignable_id),
            class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-violet-700 ' \
                   'bg-violet-100 rounded-lg hover:bg-violet-200 transition-colors opacity-0 ' \
                   'group-hover:opacity-100 transition-opacity duration-200',
            target: '_blank', rel: 'noopener') do
      concat(content_tag(:i, '', class: 'fa-light fa-eye'))
      concat(' View')
    end
  end

  def medical_history_view_button(asset)
    link_to(admin_patient_medical_history_path(asset.patient_id, asset.medical_history_id),
            class: 'inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-emerald-700 ' \
                   'bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors opacity-0 ' \
                   'group-hover:opacity-100 transition-opacity duration-200',
            target: '_blank', rel: 'noopener') do
      concat(content_tag(:i, '', class: 'fa-light fa-eye'))
      concat(' View')
    end
  end
end
