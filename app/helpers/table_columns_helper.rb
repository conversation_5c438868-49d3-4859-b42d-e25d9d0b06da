# frozen_string_literal: true

module TableColumnsHelper
  def patients_table_columns
    [
      table_column('Name', {
                     content: lambda { |patient|
                       full_name = "#{patient.first_name} #{patient.last_name}"
                       content_tag(:div, class: 'flex items-center gap-3') do
                         span_classes = 'text-sm font-medium text-gray-800 group-hover:text-gray-900'

                         image_tag(patient.image.attached? ? patient.image : 'default-avatar.webp',
                                   style: 'width: 32px; height: 32px; border-radius: 100%; flex-shrink: 0;',
                                   class: 'user-avatar') +
                         content_tag(:span, class: span_classes) do
                           link_to admin_patient_path(patient) do
                             full_name
                           end
                         end
                       end
                     },
                     class: 'min-w-48'
                   }),
      table_column('Date of Birth', {
                     content: ->(patient) { patient.date_of_birth&.strftime('%d/%m/%Y') || '--/--/----' },
                     class: 'w-28'
                   }),
      table_column('Email', {
                     content: lambda { |patient|
                       email = patient.email || 'No Email'
                       content_tag(:span, class: 'block') do
                         email
                       end
                     },
                     class: 'min-w-64'
                   }),
      table_column('Mobile Phone', {
                     content: ->(patient) { patient.mobile_phone || 'No Phone' },
                     class: 'w-32'
                   }),
      table_column('Postcode', {
                     content: ->(patient) { patient.postcode || 'No Postcode' },
                     class: 'w-20'
                   }),
      table_column('Practices', {
                     content: lambda { |patient|
                       practices = patient.practices.map(&:name).join(', ')
                       content_tag(:span, class: 'block') do
                         practices
                       end
                     },
                     class: 'min-w-32'
                   })
    ]
  end

  def prescription_table_columns
    [
      table_column('Dentist', {
                     content: lambda { |prescription|
                       content_tag(:div, class: 'flex items-center space-x-3') do
                         content_tag(:div, class: 'flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center') do
                           if prescription.dentist.image.attached?
                             image_tag(prescription.dentist.image.url, alt: prescription.dentist.full_name,
                                                                       class: 'aspect-square h-full w-full')
                           else
                             svg_attrs = {
                               class: 'lucide lucide-user w-6 h-6 text-gray-400', xmlns: 'http://www.w3.org/2000/svg',
                               width: '24', height: '24', viewBox: '0 0 24 24', fill: 'none', stroke: 'currentColor',
                               'stroke-width': '2', 'stroke-linecap': 'round', 'stroke-linejoin': 'round'
                             }
                             content_tag(:svg, svg_attrs) do
                               content_tag(:path, '', d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2') +
                               content_tag(:circle, '', cx: '12', cy: '7', r: '4')
                             end
                           end
                         end +
                         content_tag(:div, class: 'flex flex-col') do
                           badge_classes = 'inline-flex items-center border transition-colors focus:outline-none ' \
                                          'focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent w-fit mt-1 ' \
                                          'bg-orange-100 text-orange-700 hover:bg-orange-100 text-xs font-medium px-2 py-0.5 rounded-md'

                           content_tag(:span, prescription.dentist.full_name, class: 'text-sm font-medium text-gray-900') +
                           content_tag(:div, 'Dentist', class: badge_classes, 'data-v0-t': 'badge')
                         end
                       end
                     }
                   }),
      table_column('Notes', {
                     content: lambda { |prescription|
                       details = prescription.details.gsub(/<[^>]*>/, '')
                       content_tag(:span, data: { 'tippy-content': details }) do
                         truncate(details, length: 40)
                       end
                     }
                   }),
      table_column('Date', {
                     content: ->(prescription) { prescription.date.strftime('%d/%m/%Y') }
                   }),
      table_column('Medications', {
                     content: lambda { |prescription|
                       medications = prescription.medications.pluck(:name).join(', ')
                       content_tag(:span, data: { 'tippy-content': medications }) do
                         truncate(medications, length: 30)
                       end
                     }
                   })
    ]
  end

  def medications_table_columns
    [
      {
        label: 'Code',
        content: ->(medication) { extract_code_from_medication(medication) },
        class: 'w-32 font-mono text-sm'
      },
      {
        label: 'Medication Name',
        content: ->(medication) { medication.name },
        class: 'flex-1 font-medium'
      },
      {
        label: 'Brand Name',
        content: ->(medication) { extract_brand_from_medication(medication) },
        class: 'w-48'
      }
    ]
  end

  private

  def extract_code_from_medication(medication)
    medication.name.match(/^\d+/).to_s
  end

  def extract_brand_from_medication(medication)
    parts = medication.name.split(' - ')
    parts.length > 1 ? parts[1] : ''
  end
end
