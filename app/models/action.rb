# frozen_string_literal: true

# == Schema Information
#
# Table name: actions
#
#  id                :bigint           not null, primary key
#  action_type       :string           default("task")
#  actionable_type   :string           not null
#  actionable_id     :bigint           not null
#  created_by_id     :bigint
#  assigned_to_json  :json
#  title             :string
#  description       :text
#  date_due          :datetime
#  completed         :boolean          default(FALSE)
#  priority          :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  deleted_at        :datetime
#  assignment_type   :string
#  treatment_plan_id :bigint
#  appointment_id    :bigint
#
class Action < ApplicationRecord
  acts_as_paranoid

  belongs_to :actionable, polymorphic: true
  belongs_to :created_by, class_name: 'User', optional: true
  belongs_to :treatment_plan, class_name: 'CourseOfTreatment', optional: true
  belongs_to :appointment, optional: true
  has_many :action_comments

  # after_create :set_ai_title
  after_create :create_notifications
  after_save :trigger_automations
  after_save :update_actions_counter
  before_validation :set_default_priority

  validates :priority, inclusion: { in: %w[low medium high urgent], message: 'must be low, medium, high, or urgent' }

  scope :complaints, -> { where(action_type: 'complaint') }
  scope :by_priority, ->(priority) { where(priority: priority) }
  scope :urgent, -> { where(priority: 'urgent') }
  scope :high_priority, -> { where(priority: 'high') }
  scope :medium_priority, -> { where(priority: 'medium') }
  scope :low_priority, -> { where(priority: 'low') }
  scope :due_today_or_no_date, -> { where('date_due IS NULL OR DATE(date_due) = ?', Date.current) }
  scope :incomplete, -> { where(completed: false) }

  def set_ai_title
    return unless practice.present? && practice.azure_configured?

    service = Ai::ActionService.new(practice)
    new_title = service.generate_title(self)
    update(title: new_title)
  end

  def create_notifications
    # Create a toast notification for all action types
    assigned_to.each do |user|
      actions = get_notification_actions_for_type

      # Create the notification
      icon_class = Admin::ActionsController.helpers.action_icon_class(action_type, priority).to_s
      icon = icon_class.present? ? icon_class.delete_prefix('fa-light fa-') : 'bell'

      notification = Notification.create(
        recipient: user,
        title: "New #{action_type&.capitalize}",
        description: description,
        icon: icon,
        actions: actions,
        data: { type: 'action', style: action_type }
      )

      # Log notification creation for debugging
      Rails.logger.info "Created notification ##{notification.id} for action type: #{action_type}, recipient: #{user.email}"
    end
  end

  def get_notification_actions_for_type
    base_actions = [
      { text: 'Remind in', action: 'remind_in', id: id },
      { text: 'Mark Read', action: 'mark_as_read', id: id }
    ]

    base_actions << case action_type
                    when 'task'
                      { text: 'Task Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'reminder'
                      { text: 'Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'alerts'
                      { text: 'Close', action: 'mark_as_completed', id: id, primary: true }
                    when 'callback'
                      { text: 'Callback Complete', action: 'mark_as_completed', id: id, primary: true }
                    when 'complaint'
                      { text: 'Complaint Status', action: 'view_action', id: id, primary: true }
                    else
                      { text: 'View', action: 'view_action', id: id, primary: true }
                    end

    base_actions
  end

  def patient
    if actionable_type == 'Patient'
      actionable
    else
      actionable.respond_to?(:patient) ? actionable.patient : nil
    end
  end

  def practice
    if actionable.respond_to?(:practice) && actionable.practice.present?
      actionable.practice
    elsif patient.present? && patient.respond_to?(:practices) && patient.practices.any?
      patient.practices.first
    end
  end

  def assigned_to
    User.where(id: assigned_to_json)
  end

  def trigger_automations
    Automations::TriggerProcessor.call(model: self, event_type: 'action_assigned') if saved_changes[:assigned_to_json]
  end

  def self.notification_count_for_user(user)
    return 0 unless user

    # Get actions assigned to the user or created by the user
    assigned_action_ids = where(deleted_at: nil)
                          .where('assigned_to_json::text LIKE ?', "%#{user.id}%")
                          .pluck(:id)

    where(id: assigned_action_ids)
      .or(where(created_by_id: user.id))
      .incomplete
      .due_today_or_no_date
      .count
  end

  def update_actions_counter
    # Update counter for all users who are assigned to this action or created it
    users_to_update = Set.new
    users_to_update.add(created_by) if created_by.present?
    assigned_to.each { |user| users_to_update.add(user) }

    users_to_update.each do |user|
      next unless user.is_a?(User)

      pusher = PusherService.new
      actions_count = Action.notification_count_for_user(user)
      pusher.replace(
        "private-notifications.#{user.id}",
        '#actions_count',
        partial: 'layouts/admin/counter', locals: { count: actions_count, id: 'actions_count' }
      )
    end
  end

  private

  def set_default_priority
    self.priority = 'low' if priority.blank?
  end
end
