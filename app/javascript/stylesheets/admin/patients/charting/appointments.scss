.charting {
  .modal.fade.show {
    z-index: 99999 !important;
  }

  .modal-backdrop.show {
    z-index: 99998 !important;
  }

  .appointment-container, .allocated-appointment {
    position: relative;
    min-height: 207px;
    flex-direction: column;
  }

  .appointment-header {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 0 20px;
    margin-bottom: 8px;
    height: 38px;
    display: flex;
    align-items: center;
  }

  .assigned-badge {
    background-color: var(--color-blue-100);
    color: #2A4558;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 7.97436px;
    margin-left: 6px;
    height: 15.95px;
    line-height: 15.95px;
  }

  .not-booked-badge {
    background-color: #FBE099;
    color: #2A4558;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 12.5px;
    border: 1px solid #FBE099;
    margin-right: 12px;
    height: 25px;
    line-height: 25px;
  }

  .appointment-date {
    color: #272833;
    font-size: 14px;
    font-weight: 400;
    margin-right: 12px;
  }

  .appointment-type {
    color: #272833;
    font-size: 14px;
    font-weight: 400;
  }

  .margin-12 {
    margin: 0 12px;
  }

  .profile-pic {
    width: 29px;
    height: 29px;
    border-radius: 50%;
    background-color: var(--color-blue-100);
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg width='29' height='29' viewBox='0 0 29 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='14.5' cy='14.5' r='14.5' fill='url(%23pattern0)'/%3E%3Cdefs%3E%3Cpattern id='pattern0' patternContentUnits='objectBoundingBox' width='1' height='1'%3E%3Cuse xlink:href='%23image0_1349_46665' transform='scale(0.0333333)'/%3E%3C/pattern%3E%3Cimage id='image0_1349_46665' width='30' height='30' xlink:href='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAF7SURBVHgB7ZZNTsJAFMf/j3Bt4gm0JyDswEnUjYkxYcPC6MoTqCeQpScQLyCegLBgRRNNXBq5AZ6gPYGysL7XTqXFdjrDTHBhf8k005nX/vpmXucNYIIU8rhCiqbsnyFGUiI+t7CrDh6RmGNdWNjDHuawhwU0pF2Ruowu3hCDpIgj1QVFlTZiqPTQxhYWdTWPgK9SFwHX8agTplIuwg1UnGFdJzyDJ0U0VMIKhN3XGIoDPOAJT4PvGiqYxx6O8CKu5GIq4T56OJTtQ4mpjkc5LuIPbGzgDhWUUMKFrgbfcIGlhh0s40YcyMfyvUkxPONADHdtxKW0DXwhRBn3+vhM+g4euJHhM3LCVXyKe/IKXVHKZH1xYVXzKc4IZLgQ4T6acNAQY+6InCUJDTflvPnBkgz6NZv8zklyS6QYN+lfnOECJziVZ/RnOPCl/P9yFvVsFp2MrGGYclaKEzmLbXnBhZBKCLufCrEpeapfmIhDnKYp5f8BaLYO9ztAsc0AAAAASUVORK5CYII='/%3E%3C/defs%3E%3C/svg%3E");
    background-size: cover;
  }

  .menu-icon {
    margin-left: 12px;
  }

  .custom-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #8999B0;
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    position: relative;
    margin-right: 12px;
  }

  .custom-checkbox:checked::before {
    content: "\2713";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #8999B0;
    font-size: 12px;
  }

  .appointment-container {
    transition: background-color 0.3s ease;
  }

  .appointment-container.selected {
    background-color: var(--color-blue-100);
  }

  .appointment-toolbar.selected {
    background-color: var(--color-blue-100);
  }

  .accordion-body.selected {
    background-color: var(--color-blue-100);
  }

  .appointment-note-text.selected {
    background-color: var(--color-blue-100);
  }

  .circledropdown > .circledropdownbtn::after {
    content: none;
  }

  .circledropdownbtn {
    border: 1px solid #2A4558;
    border-radius: 50px;
    height: 40px;
    width: 40px;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    margin-left: 5px;
  }

  .draggable--original {
    opacity: 0.5;
  }

  .draggable-mirror {
    z-index: 1000;
  }

  .sortable-ghost {
    opacity: 0.4;
    background: #f0f9ff;
    border: 2px dashed #3b82f6;
  }

  .sortable-chosen {
    opacity: 0.8;
  }

  .sortable-drag {
    opacity: 0.8;
    transform: rotate(5deg);
  }

  .sortable-fallback {
    display: block !important;
    opacity: 0.8;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    cursor: grabbing;
    z-index: 9999;
  }

  .appointment-container[data-locked="true"] .change-dentist-item {
    display: none;
  }

  .appointment-total-container {
    background-color: #FFFFFF;
    border-radius: 20px;
    padding: 8px 20px;
    width: 700px;
    margin-left: auto;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    align-self: flex-end;
    margin-bottom: 20px;
  }

  .appointment-total-duration {
    align-items: center;
  }

  .appointment-total-time-until-booking-btn {
    display: flex;
    align-items: center;
    border: 1px solid #ccc;
    border-radius: 20px;
    overflow: hidden;
    height: 34px;
    padding: 0 10px;
  }

  .appointment-total-booking-info, .appointment-udas-info {
    display: flex;
    align-items: center;
    border: 1px solid #ccc;
    border-radius: 20px;
    overflow: hidden;
    height: 34px;
    padding: 0 10px;
    width: 150px;
  }

  .appointment-total-time-until-booking-icon {
    padding: 5px;
  }

  .time-until-booking-count,
  .time-until-booking-unit {
    border: none;
    padding: 5px;
    margin: 0;
    outline: none;
    font-size: 14px;
    width: 40px;
  }

  .time-until-booking-count {
    width: 20px;
    text-align: center;
  }

  .time-until-booking-unit {
    width: auto;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: transparent;
  }

  .time-until-booking-unit::-ms-expand {
    display: none;
  }

  .time-until-booking-unit::after {
    content: '';
    position: absolute;
    right: 10px;
    pointer-events: none;
  }

  .time-until-booking-count::-webkit-outer-spin-button,
  .time-until-booking-count::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .time-until-booking-count[type='number'] {
    -moz-appearance: textfield;
  }

  .appointment-toolbar {
    max-height: 265px;
  }

  .tool-item {
    margin: 10px 0;
  }

  .tool-item img {
    width: 24px;
    height: 24px;
  }

  .icon-green {
    color: #D1E0B7;
  }

  .icon-red {
    color: #B97172;
  }

  .chevron-icon .icon {
    transition: transform 0.3s;
  }

  .chevron-icon.rotate .icon {
    transform: rotate(180deg);
  }


  /* Apply blur effect to the textarea when the 'blurred' class is added */
  .notes-textarea.blurred {
    filter: blur(2px);
  }


  /* Popout styles adjusted for positioning to the left */
  #app-popout {
    position: absolute;
    top: 0;
    right: 60px; /* Positioning the popout to the left of the circle */
    width: 290px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    z-index: 3000;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
  }

  /* Style for app-button */
  .app-button {
    margin: 5px 5px;
    cursor: pointer;
    text-align: center;
    border: 0.5px solid #2A4558;
    color: #272833 !important;
    z-index: 3000;
    background-color: #FFFFFF;
  }

  #app-icon {
    z-index: 3000;
  }

  .hidden {
    display: none !important;
  }

  /* Overlay to cover the page when popout is open */
  .popout-overlay {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.19);
    border-radius: 16px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(11px);
    -webkit-backdrop-filter: blur(11px);
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
  }


  /* Style for appointment preview */
  #appointment-preview {
    /* Additional styles can be added here */
    pointer-events: none;
  }

  /* Style for app-button */
  .app-button {
    margin: 5px 5px;
    cursor: pointer;
    text-align: center;
  }

  /* Hidden class */
  .hidden {
    display: none !important;
  }


  .appointment-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 5000;
    background-color: transparent;
  }

  /* Blur effect for treatment-plan-container */
  .treatment-blurred > *:not(.appointment-overlay) {
    filter: blur(5px);
  }

  /* Blur effect for other appointment-containers */
  .appointment-blurred {
    filter: blur(5px);
    pointer-events: none; /* Prevent interaction with blurred elements */
  }


  /* Style to move the selected appointment-container above others */
  .selected-appointment {
    position: relative;
    z-index: 5000; /* Adjust as needed */
  }

  /* Style for Close button */
  .close-button {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 24px;
    cursor: pointer;
    z-index: 500;
    background-color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    line-height: 28px;
    text-align: center;
    border: 1px solid #ccc;
  }

  /* Ensure the app-icon is on top */
  #app-icon {
    z-index: 3000;
  }

  /* Additional styles for app-icon when selected */
  #app-icon {
    transition: background-color 0.3s ease;
  }

  .udas-input {
    border: none;
    background: transparent;
    outline: none;
    width: 20px; /* Adjust width as needed */
    text-align: center; /* Center the text inside the input */
    font-size: inherit; /* Inherit font size from parent */
    padding: 0;
    margin: 0;
    /* Remove default input number spinner controls in Chrome, Safari, Edge, and Opera */
    -webkit-appearance: none;
    -moz-appearance: textfield;
  }

  .udas-input::-webkit-outer-spin-button,
  .udas-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .appointment-type-select {
    position: relative;
    padding-right: 20px;
  }

  .appointment-type-select::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 0;
    height: 0;
    pointer-events: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #000;
    transform: translateY(-50%);
  }

  .appointment-type-select {
    width: 170px;
    background-color: transparent;
    border: none;
    text-align: center;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .xray-modal {
    display: none;
    position: fixed;
    z-index: 8050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
  }

  .xray-popover {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    max-height: 90%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .xray-popover-title-text {
    font-size: 1.5rem;
    font-weight: bold;
  }

  .xray-popover-content {
    display: flex;
    flex-wrap: wrap;
  }

  .xray-popover-image {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
  }

  .xray-details {
    margin-top: 20px;
  }

  .xray-label {
    font-weight: bold;
    margin-top: 10px;
  }

  .xray-field {
    margin-bottom: 10px;
  }

  .badge {
    padding: 5px 10px;
    border-radius: 15px;
    background-color: var(--color-blue-100);
    color: #151515;
    font-size: 0.9rem;
  }

  .xray-popover-close:hover {
    opacity: 0.7;
  }

  .allocated-appointment p {
    font-weight: 500;
    color: rgba(137, 153, 176, 1);
    font-size: 16px;
    margin-bottom: 0;
    line-height: 24px;
  }

  .allocated-appointment-button {
    background-color: rgba(200, 165, 163, 1);
    border: 1px solid rgba(42, 69, 88, 1);
    padding: 8px 32px;
    font-size: 14px;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    margin-top: 30px;
  }

  @media (min-width: 768px) {
    .xray-popover-content {
      flex-wrap: nowrap;
    }
    .xray-details {
      margin-top: 0;
    }
  }
}
