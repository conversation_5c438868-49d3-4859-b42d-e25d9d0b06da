function openOffcanvas(id) {
  closeAllOffcanvases();

  document.getElementById(id).classList.remove('translate-x-full')
}
function closeOffcanvas(id) {
  const offcanvas = document.getElementById(id);

  if (!offcanvas) return;

  offcanvas.classList.add('translate-x-full');
  document.getElementById(`${id}-container`).replaceChildren();
}

function closeAllOffcanvases() {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => closeOffcanvas(id));
}

document.addEventListener('DOMContentLoaded', () => {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => {
    if (document.getElementById(id)) {
      document.getElementById(`${id}-close`).addEventListener('click', () => closeOffcanvas(id))
    }
  })

  // Check if we need to open slot finder after page load (from CRM book appointment)
  const pendingPatientId = sessionStorage.getItem('openSlotFinderForPatient');
  if (pendingPatientId) {
    // Clear the flag
    sessionStorage.removeItem('openSlotFinderForPatient');

    // Wait a moment for the page to fully load, then trigger slot finder
    setTimeout(() => {
      const dateToday = new Date();

      $.ajax({
        url: `/admin/calendar_bookings/slot_finder_offcanvas`,
        type: 'GET',
        dataType: 'script',
        data: {
          date: dateToday,
          patient_id: pendingPatientId
        }
      });
    }, 1000);
  }

  // Listen for messages from parent window (CRM book appointment)
  window.addEventListener('message', function(event) {
    // Verify origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    if (event.data.action === 'setPracticeAndOpenSlotFinder' && event.data.patientId && event.data.practiceId) {
      // Store patient ID for after reload
      sessionStorage.setItem('openSlotFinderForPatient', event.data.patientId);

      // First set the practice context in this tab
      fetch('/admin/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({practice_id: event.data.practiceId})
      }).then(response => {
        if (response.ok) {
          // Practice context set, now reload the page to show the correct practice
          window.location.reload();
        } else {
          console.error('Failed to set practice context in new tab');
          // Clear the stored patient ID if practice setting failed
          sessionStorage.removeItem('openSlotFinderForPatient');
        }
      }).catch(error => {
        console.error('Error setting practice context in new tab:', error);
        // Clear the stored patient ID if practice setting failed
        sessionStorage.removeItem('openSlotFinderForPatient');
      });
    }
  });
});

window.openOffcanvas = openOffcanvas
window.closeOffcanvas = closeOffcanvas
window.closeAllOffcanvases = closeAllOffcanvases
