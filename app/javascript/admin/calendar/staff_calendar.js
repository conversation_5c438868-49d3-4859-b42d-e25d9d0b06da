function openOffcanvas(id) {
  closeAllOffcanvases();

  document.getElementById(id).classList.remove('translate-x-full')
}
function closeOffcanvas(id) {
  const offcanvas = document.getElementById(id);

  if (!offcanvas) return;

  offcanvas.classList.add('translate-x-full');
  document.getElementById(`${id}-container`).replaceChildren();
}

function closeAllOffcanvases() {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => closeOffcanvas(id));
}

document.addEventListener('DOMContentLoaded', () => {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => {
    if (document.getElementById(id)) {
      document.getElementById(`${id}-close`).addEventListener('click', () => closeOffcanvas(id))
    }
  })

  // Check if we should auto-open slot finder (from CRM book appointment)
  const urlParams = new URLSearchParams(window.location.search);
  const patientId = urlParams.get('patient_id');

  if (patientId) {
    // Wait a moment for the page to fully load, then trigger slot finder
    setTimeout(() => {
      const dateToday = new Date();

      $.ajax({
        url: `/admin/calendar_bookings/slot_finder_offcanvas`,
        type: 'GET',
        dataType: 'script',
        data: {
          date: dateToday,
          patient_id: patientId
        }
      });
    }, 1000);
  }
});

window.openOffcanvas = openOffcanvas
window.closeOffcanvas = closeOffcanvas
window.closeAllOffcanvases = closeAllOffcanvases
