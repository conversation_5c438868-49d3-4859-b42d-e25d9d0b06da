function openOffcanvas(id) {
  closeAllOffcanvases();

  document.getElementById(id).classList.remove('translate-x-full')
}
function closeOffcanvas(id) {
  const offcanvas = document.getElementById(id);

  if (!offcanvas) return;

  offcanvas.classList.add('translate-x-full');
  document.getElementById(`${id}-container`).replaceChildren();
}

function closeAllOffcanvases() {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => closeOffcanvas(id));
}

document.addEventListener('DOMContentLoaded', () => {
  ['slot-finder-offcanvas', 'reschedule-offcanvas', 'search-offcanvas', 'drag-offcanvas'].forEach(id => {
    if (document.getElementById(id)) {
      document.getElementById(`${id}-close`).addEventListener('click', () => closeOffcanvas(id))
    }
  })

  // Listen for messages from parent window (CRM book appointment)
  window.addEventListener('message', function(event) {
    // Verify origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    if (event.data.action === 'openSlotFinder' && event.data.patientId) {
      // Trigger slot finder with the patient ID
      const dateToday = new Date();

      $.ajax({
        url: `/admin/calendar_bookings/slot_finder_offcanvas`,
        type: 'GET',
        dataType: 'script',
        data: {
          date: dateToday,
          patient_id: event.data.patientId
        }
      });
    }
  });
});

window.openOffcanvas = openOffcanvas
window.closeOffcanvas = closeOffcanvas
window.closeAllOffcanvases = closeAllOffcanvases
