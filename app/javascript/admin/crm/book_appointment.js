$(document).ready(function () {
  // Handle book appointment button clicks from CRM card header
  $(document).on('click', '.book-appointment-button', function(e) {
    e.preventDefault();
    
    const patientId = $(this).data('patient-id');
    const practiceId = $(this).data('practice-id');
    
    if (!patientId || !practiceId) {
      console.error('Missing patient ID or practice ID');
      return;
    }
    
    // Build the calendar URL with day mode, patient_id, and practice_id
    const calendarUrl = `/admin/calendar_bookings/staff_calendar?mode=day&patient_id=${patientId}&practice_id=${practiceId}`;

    // Open in new tab
    const newTab = window.open(calendarUrl, '_blank');

    // Wait a moment for the page to load, then trigger slot finder
    setTimeout(() => {
      // Send a message to the new tab to open slot finder
      if (newTab && !newTab.closed) {
        newTab.postMessage({
          action: 'openSlotFinder',
          patientId: patientId
        }, window.location.origin);
      }
    }, 2000);
  });
});
