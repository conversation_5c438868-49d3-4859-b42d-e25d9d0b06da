$(document).ready(function () {
  // Handle book appointment button clicks from CRM card header
  $(document).on('click', '.book-appointment-button', function(e) {
    e.preventDefault();
    
    const patientId = $(this).data('patient-id');
    const practiceId = $(this).data('practice-id');
    
    if (!patientId || !practiceId) {
      console.error('Missing patient ID or practice ID');
      return;
    }
    
    // First set the practice context
    fetch('/admin/sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({practice_id: practiceId})
    }).then(response => {
      if (response.ok) {
        // Build the calendar URL with day mode and patient_id
        const calendarUrl = `/admin/calendar_bookings/staff_calendar?mode=day&patient_id=${patientId}`;
        
        // Open in new tab
        const newTab = window.open(calendarUrl, '_blank');
        
        // Wait a moment for the page to load, then trigger slot finder
        setTimeout(() => {
          // Send a message to the new tab to open slot finder
          if (newTab && !newTab.closed) {
            newTab.postMessage({
              action: 'openSlotFinder',
              patientId: patientId
            }, window.location.origin);
          }
        }, 2000);
      } else {
        console.error('Failed to set practice context');
      }
    }).catch(error => {
      console.error('Error setting practice context:', error);
    });
  });
});
