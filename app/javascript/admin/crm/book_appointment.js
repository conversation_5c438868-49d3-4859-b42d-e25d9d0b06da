$(document).ready(function () {
  // Handle book appointment button clicks from CRM card header
  $(document).on('click', '.book-appointment-button', function(e) {
    e.preventDefault();
    
    const patientId = $(this).data('patient-id');
    const practiceId = $(this).data('practice-id');
    
    if (!patientId || !practiceId) {
      console.error('Missing patient ID or practice ID');
      return;
    }
    
    // Build the calendar URL with day mode and patient_id
    const calendarUrl = `/admin/calendar_bookings/staff_calendar?mode=day&patient_id=${patientId}`;

    // Open in new tab
    const newTab = window.open(calendarUrl, '_blank');

    // Wait a moment for the page to load, then set practice and trigger slot finder
    setTimeout(() => {
      // Send a message to the new tab to set practice and open slot finder
      if (newTab && !newTab.closed) {
        newTab.postMessage({
          action: 'setPracticeAndOpenSlotFinder',
          patientId: patientId,
          practiceId: practiceId
        }, window.location.origin);
      }
    }, 2000);
  });
});
