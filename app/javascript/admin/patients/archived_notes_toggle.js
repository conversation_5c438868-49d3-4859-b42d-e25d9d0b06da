// Archived Notes Toggle Functionality
$(document).ready(function() {
  // Handle toggle archived notes button click
  $(document).on('click', '#toggle-archived-notes', function() {
    const $button = $(this);
    const patientId = window.location.pathname.split('/').pop();
    const showingArchived = $button.data('showing-archived');
    
    // Toggle the state
    const newState = !showingArchived;
    $button.data('showing-archived', newState);
    
    // Update button appearance
    if (newState) {
      $button.addClass('bg-green-100 text-green-600 border-green-200')
             .removeClass('bg-red-100 text-red-600 border-red-200')
             .attr('title', 'Show active notes');
    } else {
      $button.addClass('bg-red-100 text-red-600 border-red-200')
             .removeClass('bg-green-100 text-green-600 border-green-200')
             .attr('title', 'Show archived notes');
    }
    
    // Show loading state
    const $notesContainer = $('#patient-notes-container');
    $notesContainer.html('<div class="flex justify-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div></div>');
    
    // Fetch the appropriate notes
    $.ajax({
      url: `/admin/patients/${patientId}/notes`,
      method: 'GET',
      data: { archived: newState },
      dataType: 'json',
      success: function(response) {
        if (response.notes.length === 0) {
          $notesContainer.html('<div class="text-center py-8 text-gray-500">No ' + (newState ? 'archived' : 'active') + ' notes found</div>');
          return;
        }
        
        // Clear the container
        $notesContainer.empty();
        
        // Append each note
        response.notes.forEach(function(note) {
          const noteHtml = renderNoteHtml(note);
          $notesContainer.append(noteHtml);
        });
        
        // Re-initialize note menus for the newly added notes
        if (typeof initializeNoteMenus === 'function') {
          initializeNoteMenus();
        }
      },
      error: function(xhr, status, error) {
        $notesContainer.html('<div class="text-center py-8 text-red-500">Error loading notes. Please try again.</div>');
      }
    });
  });

  // Handle archive/unarchive note clicks using jQuery AJAX
  $(document).on('click', 'a[href*="/patient_notes/"][href*="/archive"]', function(e) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    const $link = $(this);
    const url = $link.attr('href');
    const noteId = url.match(/\/(\d+)\/archive/)[1];
    const $noteElement = $(`[data-note-id="${noteId}"]`);

    // Check if we're currently viewing archived or active notes
    const $toggleButton = $('#toggle-archived-notes');
    const showingArchived = $toggleButton.data('showing-archived');

    // Disable the link temporarily to prevent double clicks
    $link.css('pointer-events', 'none');

    // Make AJAX request to archive/unarchive the note
    $.ajax({
      url: url,
      method: 'PATCH',
      data: {
        authenticity_token: $('meta[name="csrf-token"]').attr('content')
      },
      dataType: 'json',
      success: function(response) {
        // Remove the note from the current view with a fade effect
        if ($noteElement.length) {
          $noteElement.fadeOut(300, function() {
            $(this).remove();

            // Check if there are any notes left
            const $notesContainer = $('#patient-notes-container');
            const remainingNotes = $notesContainer.find('[data-note-id]').length;

            if (remainingNotes === 0) {
              const messageText = showingArchived ? 'No archived notes found' : 'No active notes found';
              $notesContainer.html(`<div class="text-center py-8 text-gray-500">${messageText}</div>`);
            }
          });
        }

        // Show success message
        const actionText = showingArchived ? 'unarchived' : 'archived';
        if (typeof toastr !== 'undefined') {
          toastr.success(`Note ${actionText} successfully`);
        }

        // Refresh the pinned notes dropdown and count (since archiving affects pinned status)
        refreshPinnedNotesDropdown();
      },
      error: function(xhr, status, error) {
        // Re-enable the link
        $link.css('pointer-events', 'auto');

        // Show error message
        if (typeof toastr !== 'undefined') {
          toastr.error('Failed to archive/unarchive note. Please try again.');
        }
      }
    });

    // Return false to ensure no default behavior
    return false;
  });

  // Handle pin/unpin note clicks using jQuery AJAX
  $(document).on('click', 'a[href*="/patient_notes/"][href*="/pin"]', function(e) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    const $link = $(this);
    const url = $link.attr('href');
    const noteId = url.match(/\/(\d+)\/pin/)[1];
    const $noteElement = $(`[data-note-id="${noteId}"]`);
    const currentText = $link.text().trim();
    const isPinning = currentText === 'Pin note';

    // Disable the link temporarily to prevent double clicks
    $link.css('pointer-events', 'none');

    $.ajax({
      url: url,
      method: 'PATCH',
      dataType: 'json',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        // Update the link text
        $link.text(isPinning ? 'Unpin note' : 'Pin note');

        // Show success message
        const actionText = isPinning ? 'pinned' : 'unpinned';
        if (typeof toastr !== 'undefined') {
          toastr.success(`Note ${actionText} successfully`);
        }

        // Hide the menu
        $link.closest('.note-menu').addClass('hidden');

        // Refresh the pinned notes dropdown and count
        refreshPinnedNotesDropdown();
      },
      error: function(xhr, status, error) {
        console.error('Error pinning/unpinning note:', error);

        // Show error message
        if (typeof toastr !== 'undefined') {
          toastr.error('Failed to pin/unpin note. Please try again.');
        }
      },
      complete: function() {
        // Re-enable the link
        $link.css('pointer-events', 'auto');
      }
    });

    // Return false to ensure no default behavior
    return false;
  });

  // Helper function to render a note HTML
  function renderNoteHtml(note) {
    // Format date in the same way as the original template
    const createdAt = new Date(note.created_at)
      .toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
      .replace(',', '');
    
    // Determine color - default to blue if not specified
    const color = note.color || 'blue';
    
    // Generate shadow color based on note color
    let shadowColorRGB;
    if (color === 'amber' || color === 'yellow') {
      shadowColorRGB = '251, 191, 36';
    } else if (color === 'blue') {
      shadowColorRGB = '59, 130, 246';
    } else if (color === 'green') {
      shadowColorRGB = '34, 197, 94';
    } else {
      shadowColorRGB = '244, 114, 182'; // pink default
    }
    
    return `
      <div class="bg-${color}-100 rounded-lg p-4 relative border-0 shadow-lg hover:scale-[1.01] transition-transform duration-200" data-note-id="${note.id}" style="box-shadow: rgba(${shadowColorRGB}, 0.1) 0px 10px 15px -3px, rgba(${shadowColorRGB}, 0.2) 0px 4px 6px -4px;">
        <div class="absolute right-0 top-0 w-8 h-8 bg-${color}-200/50 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>
        <div class="flex justify-between items-start mb-2">
          <span class="text-[11px] px-2 py-0.5 bg-${color}-200/80 rounded-full text-${color}-800 font-medium">${note.user_name || 'Admin User'}</span>
          <div class="flex gap-1">
            <div class="relative">
              <button class="note-menu-button inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 text-${color}-600 hover:text-${color}-800 hover:bg-${color}-200/50" data-note-id="${note.id}">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis h-3.5 w-3.5">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                  <circle cx="5" cy="12" r="1"></circle>
                </svg>
              </button>
              <div class="note-menu absolute right-0 z-50 hidden w-36 mt-1 py-1 bg-white rounded-md shadow-lg" data-note-id="${note.id}">
                ${note.archived ?
                  `<a href="/admin/patient_notes/${note.id}/archive" class="flex items-center px-3 py-1.5 text-[13px] text-red-600 hover:bg-gray-100 w-full text-left">Unarchive note</a>` :
                  `<a href="/admin/patient_notes/${note.id}/pin" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left" data-remote="true" data-method="patch">${note.pinned ? 'Unpin note' : 'Pin note'}</a>
                   <button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left update-note-btn" data-note-id="${note.id}" onclick="openNoteModal(${note.id}, true)">Update note</button>
                   <a href="/admin/patient_notes/${note.id}/archive" class="flex items-center px-3 py-1.5 text-[13px] text-red-600 hover:bg-gray-100 w-full text-left">Archive note</a>`
                }
              </div>
            </div>
            <div class="relative">
              <button class="note-color-picker-btn inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 text-${color}-600 hover:text-${color}-800 hover:bg-${color}-200/50" data-note-id="${note.id}">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-droplet h-3.5 w-3.5">
                  <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
                </svg>
              </button>
              <div class="note-color-picker absolute right-0 top-full mt-1 p-2 bg-white rounded-md shadow-lg z-10 hidden flex-wrap gap-2" style="width: 100px;">
                <button type="button" class="note-color-option h-6 w-6 rounded-full bg-yellow-200 hover:ring-2 hover:ring-yellow-400 hover:ring-offset-2" data-color="yellow" data-note-id="${note.id}"></button>
                <button type="button" class="note-color-option h-6 w-6 rounded-full bg-blue-200 hover:ring-2 hover:ring-blue-400 hover:ring-offset-2" data-color="blue" data-note-id="${note.id}"></button>
                <button type="button" class="note-color-option h-6 w-6 rounded-full bg-green-200 hover:ring-2 hover:ring-green-400 hover:ring-offset-2" data-color="green" data-note-id="${note.id}"></button>
                <button type="button" class="note-color-option h-6 w-6 rounded-full bg-pink-200 hover:ring-2 hover:ring-pink-400 hover:ring-offset-2" data-color="pink" data-note-id="${note.id}"></button>
              </div>
            </div>
          </div>
        </div>
        <h4 class="font-medium text-[14px] text-${color}-900">${note.title || ''}</h4>
        <p class="text-[11px] text-${color}-700/70 mb-2">${createdAt}</p>
        <p class="text-[13px] text-${color}-800">${note.text || ''}</p>
      </div>
    `;
  }

  // Function to refresh the pinned notes dropdown and count
  function refreshPinnedNotesDropdown() {
    // Get the patient ID from the dropdown container
    const $dropdownContainer = $('.notes-dropdown-container');
    const patientId = $dropdownContainer.data('patient-id');

    if (!patientId) {
      console.error('Patient ID not found for refreshing pinned notes');
      return;
    }

    // Fetch updated pinned notes from the server
    $.ajax({
      url: `/admin/patients/${patientId}/notes`,
      method: 'GET',
      dataType: 'json',
      data: { pinned_only: true },
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          updatePinnedNotesUI(response.notes);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error refreshing pinned notes:', error);
      }
    });
  }

  // Function to update the pinned notes UI
  function updatePinnedNotesUI(pinnedNotes) {
    const $dropdownMenu = $('.notes-dropdown-menu');
    const $countBadge = $('.notes-dropdown-trigger .flex.items-center.justify-center');

    // Update the count badge
    $countBadge.text(pinnedNotes.length);

    // Clear existing pinned notes (everything after the mute section)
    // Find all elements with data-note-id and remove them
    $dropdownMenu.find('[data-note-id]').remove();

    // Also remove any "No pinned notes" message
    $dropdownMenu.find('.text-center.text-gray-400').remove();

    if (pinnedNotes.length > 0) {
      // Add each pinned note after the mute section
      const $muteContainer = $dropdownMenu.find('.mute-notes-container');
      pinnedNotes.forEach(function(note) {
        const noteHtml = renderPinnedNoteHtml(note);
        $muteContainer.after(noteHtml);
      });
    } else {
      // Show "No pinned notes" message after the mute section
      const $muteContainer = $dropdownMenu.find('.mute-notes-container');
      $muteContainer.after('<div class="p-4 text-center text-gray-400 text-xs">No pinned notes</div>');
    }
  }

  // Function to render a pinned note for the dropdown
  function renderPinnedNoteHtml(note) {
    // Format date
    const createdAt = new Date(note.created_at);
    const timeAgo = getTimeAgo(createdAt);

    // Determine color - default to blue if not specified
    const color = note.color || 'blue';

    // Generate shadow color based on note color
    let shadowColorRGB;
    if (color === 'amber' || color === 'yellow') {
      shadowColorRGB = '251, 191, 36';
    } else if (color === 'blue') {
      shadowColorRGB = '59, 130, 246';
    } else if (color === 'green') {
      shadowColorRGB = '34, 197, 94';
    } else {
      shadowColorRGB = '244, 114, 182'; // pink default
    }

    return `
      <div class="bg-${color}-100 rounded-lg p-3 mb-2 relative border-0 shadow-sm hover:scale-[1.01] transition-transform duration-200" data-note-id="${note.id}" style="box-shadow: rgba(${shadowColorRGB}, 0.1) 0px 10px 15px -3px, rgba(${shadowColorRGB}, 0.2) 0px 4px 6px -4px;">
        <div class="absolute right-0 top-0 w-6 h-6 bg-${color}-200/50 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>
        <div class="font-medium text-[14px] text-${color}-900">${note.title || 'Note'}</div>
        <div class="text-[13px] text-${color}-800">${note.text || ''}</div>
        <div class="mt-1 text-[11px] text-${color}-700/70">Pinned ${timeAgo} ago</div>
      </div>
    `;
  }

  // Helper function to calculate time ago
  function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months`;
    return `${Math.floor(diffInSeconds / 31536000)} years`;
  }
});
