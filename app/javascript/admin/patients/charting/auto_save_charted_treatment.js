$(document).ready(function () {
  let saveTimeouts = {}; // Store timeouts for debouncing

  // Function to auto-save the form
  function autoSaveChartedTreatment(form, delayMs = 500) {
    const formData = new FormData(form);
    const chartedTreatmentId = formData.get('charted_treatment[id]');
    
    console.log('Auto-saving charted treatment:', chartedTreatmentId);
    
    // Clear existing timeout for this treatment
    if (saveTimeouts[chartedTreatmentId]) {
      clearTimeout(saveTimeouts[chartedTreatmentId]);
    }
    
    // Set new timeout for debouncing
    saveTimeouts[chartedTreatmentId] = setTimeout(() => {
      // Sync all TinyMCE editors in this form before saving
      $(form).find('.tinymce_editor').each(function() {
        const editorId = $(this).attr('id');
        if (typeof tinymce !== 'undefined' && editorId) {
          const editor = tinymce.get(editorId);
          if (editor) {
            editor.save(); // Sync content to textarea
            console.log('Synced TinyMCE editor:', editorId);
          }
        }
      });
      
      $.ajax({
        url: form.action,
        method: 'PATCH',
        data: $(form).serialize(),
        headers: {
          'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          console.log('Auto-save response:', response);
          if (response.success) {
            if (typeof toastr !== 'undefined') {
              toastr.success('Charted treatment updated successfully');
            }
            
            // Update practitioner avatar if provided
            if (response.practitioner_avatar) {
              const avatarImg = $(form).find('.practitioner-avatar img');
              if (avatarImg.length) {
                avatarImg.attr('src', response.practitioner_avatar);
              }
            }
            
            // Update treatment note preview if notes are changed
            if (response.notes_changed) {
              const notes = $(form).find('textarea[name="charted_treatment[notes]"]').val();
              updateTreatmentNotePreview(form, notes);
            }
          } else {
            console.error('Auto-save failed:', response);
            if (typeof toastr !== 'undefined') {
              toastr.error(response.error || 'Failed to update charted treatment');
            }
          }
        },
        error: function(xhr) {
          console.error('Auto-save error:', xhr);
          if (typeof toastr !== 'undefined') {
            const errorMessage = xhr.responseJSON?.error || 'Failed to update charted treatment';
            toastr.error(errorMessage);
          }
        }
      });
    }, delayMs);
  }

  // Function to update display elements when form inputs change
  function updateDisplayElements(changedInput, form) {
    const inputName = changedInput.attr('name');
    const inputValue = changedInput.val();
    let shouldUpdateTotals = false;
    
    // Update duration display
    if (inputName === 'charted_treatment[duration]') {
      const durationDisplay = $(form).closest('.accordion-item').find('.edit-duration');
      if (durationDisplay.length) {
        const newText = (inputValue || '0') + ' mins';
        durationDisplay.text(newText);
        console.log('Updated duration display to:', newText);
        shouldUpdateTotals = true;
      }
    }
    
    // Update price display
    if (inputName === 'charted_treatment[override_price]') {
      const priceDisplay = $(form).closest('.accordion-item').find('.edit-price');
      if (priceDisplay.length) {
        const numericValue = parseFloat(inputValue) || 0;
        const formattedPrice = '£' + numericValue.toFixed(2);
        priceDisplay.text(formattedPrice);
        console.log('Updated price display to:', formattedPrice);
        shouldUpdateTotals = true;
      }
    }
    
    // Update totals if duration or price changed
    if (shouldUpdateTotals) {
      console.log('Updating totals after display change');
      if (typeof window.updateTotals === 'function') {
        window.updateTotals();
      }
      if (typeof window.changeAppointmentsTotals === 'function') {
        window.changeAppointmentsTotals();
      }
    }
  }

  // Function to update treatment note preview
  function updateTreatmentNotePreview(form, notes) {
    const accordionItem = $(form).closest('.accordion-item');
    const existingPreview = accordionItem.find('.treatment-note-preview-row');
    
    // Get text content (strip HTML)
    const textContent = $('<div>').html(notes).text().trim().replace(/\s+/g, ' ');
    
    if (textContent.length > 0) {
      // Notes exist - create or update preview
      if (existingPreview.length) {
        // Update existing preview
        const previewSpan = existingPreview.find('.treatment-note-preview-text');
        const wasExpanded = previewSpan.hasClass('expanded');
        
        previewSpan.attr('data-html-content', notes);
        previewSpan.attr('data-text-content', textContent);
        
        // Update displayed content based on current state
        if (wasExpanded) {
          // If currently expanded, show updated HTML content
          previewSpan.html(notes);
          console.log('Updated expanded note preview with HTML content');
        } else {
          // If collapsed, show updated text content
          previewSpan.text(textContent);
          console.log('Updated collapsed note preview with text content');
        }
      } else {
        // Create new preview
        const previewHtml = `
          <div class="treatment-note-preview-row mt-1 text-[11px] text-neutral-500 line-clamp-1 pr-2 relative group">
            <span class="treatment-note-preview-text"
                  data-html-content="${notes.replace(/"/g, '&quot;')}"
                  data-text-content="${textContent}">
              ${textContent}
            </span>
          </div>
        `;
        
        // Find the insertion point (after the header, before the accordion panel)
        const insertionPoint = accordionItem.find('form[id^="update-charting-form-"]');
        if (insertionPoint.length) {
          insertionPoint.before(previewHtml);
          console.log('Created new note preview');
        }
      }
    } else {
      // No notes - remove preview if it exists
      if (existingPreview.length) {
        existingPreview.remove();
        console.log('Removed note preview (no notes)');
      }
    }
  }

  // Auto-save for regular form inputs (immediate on change)
  $(document).on('change', 'form[id^="update-charting-form-"] input[type="number"], form[id^="update-charting-form-"] input[type="date"], form[id^="update-charting-form-"] select, form[id^="update-charting-form-"] input[type="text"]:not(.notes-textarea)', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Regular input changed:', $(this).attr('name'));
      
      // Update display elements for duration and price
      updateDisplayElements($(this), form);
      
      autoSaveChartedTreatment(form, 300); // 300ms delay for regular inputs
    }
  });

  // Auto-save for notes (on blur - when user leaves the field)
  $(document).on('blur', 'form[id^="update-charting-form-"] .notes-textarea, form[id^="update-charting-form-"] .tinymce_editor', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Notes field blur:', $(this).attr('id'));
      
      // For TinyMCE editors, we need to sync content before saving
      const editorId = $(this).attr('id');
      let notes = '';
      if (typeof tinymce !== 'undefined' && editorId) {
        const editor = tinymce.get(editorId);
        if (editor) {
          // Sync TinyMCE content to textarea
          editor.save();
          notes = editor.getContent();
          console.log('Synced TinyMCE content for:', editorId);
        }
      } else {
        notes = $(this).val();
      }
      
      // Update note preview immediately
      updateTreatmentNotePreview(form, notes);
      
      autoSaveChartedTreatment(form, 500); // 500ms delay for notes
    }
  });

  // Auto-save for checkbox inputs (immediate)
  $(document).on('change', 'form[id^="update-charting-form-"] input[type="checkbox"]', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Checkbox changed:', $(this).attr('name'));
      autoSaveChartedTreatment(form, 100); // 100ms delay for checkboxes
    }
  });

  // Enhanced TinyMCE event handling
  $(document).on('tinymce:blur', function(e) {
    console.log('TinyMCE blur event:', e);
    const editorId = e.target.id;
    const editor = tinymce.get(editorId);
    if (editor && $(e.target).hasClass('tinymce_editor')) {
      const form = $(e.target).closest('form')[0];
      if (form && form.id.startsWith('update-charting-form-')) {
        console.log('TinyMCE blur auto-save triggered for:', editorId);
        editor.save(); // Sync content to textarea
        autoSaveChartedTreatment(form, 500);
      }
    }
  });

  // Listen for TinyMCE initialization and set up blur events
  $(document).on('tinymce:init', function(e) {
    console.log('TinyMCE init event:', e);
    const editor = e.originalEvent.detail.editor;
    if (editor && $(editor.getElement()).hasClass('tinymce_editor')) {
      console.log('Setting up blur listener for TinyMCE editor:', editor.id);
      
      // Set up blur event
      editor.on('blur', function() {
        console.log('TinyMCE editor blur:', editor.id);
        const form = $(editor.getElement()).closest('form')[0];
        if (form && form.id.startsWith('update-charting-form-')) {
          editor.save(); // Sync content to textarea
          const notes = editor.getContent();
          updateTreatmentNotePreview(form, notes);
          autoSaveChartedTreatment(form, 500);
        }
      });
      
      // Set up content change events for empty-to-content scenarios
      editor.on('input', function() {
        const content = editor.getContent().trim();
        if (content.length > 0) {
          console.log('TinyMCE content changed (has content):', editor.id);
          const form = $(editor.getElement()).closest('form')[0];
          if (form && form.id.startsWith('update-charting-form-')) {
            editor.save(); // Sync content to textarea
            const notes = editor.getContent();
            updateTreatmentNotePreview(form, notes);
            autoSaveChartedTreatment(form, 1000); // Longer delay for input events
          }
        }
      });
      
      // Set up keyup events for better responsiveness
      editor.on('keyup', function() {
        const content = editor.getContent().trim();
        if (content.length > 0) {
          console.log('TinyMCE keyup with content:', editor.id);
          const form = $(editor.getElement()).closest('form')[0];
          if (form && form.id.startsWith('update-charting-form-')) {
            editor.save(); // Sync content to textarea
            const notes = editor.getContent();
            updateTreatmentNotePreview(form, notes);
            autoSaveChartedTreatment(form, 1500); // Longer delay for keyup events
          }
        }
      });
      
      // Set up change event for comprehensive content change detection
      editor.on('change', function() {
        console.log('TinyMCE change event:', editor.id);
        const form = $(editor.getElement()).closest('form')[0];
        if (form && form.id.startsWith('update-charting-form-')) {
          editor.save(); // Sync content to textarea
          const notes = editor.getContent();
          updateTreatmentNotePreview(form, notes);
          autoSaveChartedTreatment(form, 800); // Medium delay for change events
        }
      });
      
      // Set up paste event for when content is pasted
      editor.on('paste', function() {
        console.log('TinyMCE paste event:', editor.id);
        // Use a timeout to ensure pasted content is processed
        setTimeout(() => {
          const form = $(editor.getElement()).closest('form')[0];
          if (form && form.id.startsWith('update-charting-form-')) {
            editor.save(); // Sync content to textarea
            const notes = editor.getContent();
            updateTreatmentNotePreview(form, notes);
            autoSaveChartedTreatment(form, 600); // Quick save after paste
          }
        }, 100);
      });
    }
  });

  // Alternative: Listen for direct TinyMCE blur events
  $(document).on('focusout', '.tinymce_editor', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Focusout event on TinyMCE textarea:', $(this).attr('id'));
      
      const editorId = $(this).attr('id');
      if (typeof tinymce !== 'undefined' && editorId) {
        const editor = tinymce.get(editorId);
        if (editor) {
          editor.save(); // Sync content to textarea
          const notes = editor.getContent();
          updateTreatmentNotePreview(form, notes);
          console.log('Manual sync and save for:', editorId);
          autoSaveChartedTreatment(form, 500);
        }
      }
    }
  });

  // Listen for custom TinyMCE blur event triggered from tinymce.js
  $(document).on('tinymce-blur-autosave', '.tinymce_editor', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Custom TinyMCE blur event on:', $(this).attr('id'));
      
      const editorId = $(this).attr('id');
      if (typeof tinymce !== 'undefined' && editorId) {
        const editor = tinymce.get(editorId);
        if (editor) {
          editor.save(); // Sync content to textarea
          const notes = editor.getContent();
          updateTreatmentNotePreview(form, notes);
          console.log('Custom event sync and save for:', editorId);
          autoSaveChartedTreatment(form, 500);
        }
      }
    }
  });

  // Real-time display updates for duration and price (on input/keyup)
  $(document).on('input keyup', 'form[id^="update-charting-form-"] .charted_treatment_duration, form[id^="update-charting-form-"] .charted_treatment_override_price', function() {
    const form = $(this).closest('form')[0];
    if (form && form.id.startsWith('update-charting-form-')) {
      console.log('Real-time update for:', $(this).attr('name'));
      updateDisplayElements($(this), form);
    }
  });

  // Cleanup timeouts when page unloads
  $(window).on('beforeunload', function() {
    Object.values(saveTimeouts).forEach(timeout => clearTimeout(timeout));
  });
});
