$(document).ready(function () {
  function updateAppointmentNumbers() {
    $('.appointment-container .apptid').each(function (index, element) {
      $(element).text(index + 1);
    });
  }

  $(document).on('click', '#newappt', function (event) {
    event.preventDefault();

    const $newApptElement = $(this);
    const courseOfTreatmentId = $newApptElement.data('courseOfTreatmentId');
    const patientId = $newApptElement.data('patientId');
    const currentPosition = $newApptElement.data('currentPosition');
    const $courseOfTreatmentContainer = $newApptElement.closest('#course-of-treatment-container');

    // Check number of appointments before creation
    const $apptContainers = $courseOfTreatmentContainer.find('.appointment-container');

    // Calculate the new position - insert after the current appointment
    const insertPosition = currentPosition + 1;

    $.ajax({
      type: 'POST',
      url: `/admin/patients/${patientId}/charting/create_appointment`,
      contentType: 'application/json',
      dataType: 'json',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      data: JSON.stringify({
        charting_appointment: {
          course_of_treatment_id: courseOfTreatmentId,
          position: insertPosition,
        }
      }),
      success: function (data) {
        if (data.error) {
          toastr.error("You are not allowed to add new appointments.");
          console.error("Error:", data.error);
          return;
        }

        const temp = document.createElement('div');
        temp.innerHTML = data.html;
        const newAppointmentElement = temp.firstElementChild;

        // Find the appointment-block that contains the newappt button
        const appointmentBlock = $newApptElement.closest('.appointment-block')[0];

        // Insert the new appointment after the current appointment-block
        appointmentBlock.parentNode.insertBefore(newAppointmentElement, appointmentBlock.nextSibling);

        $('.appointment-container').removeClass('selected');
        $('.appointment-toolbar').removeClass('selected');
        $(newAppointmentElement).find('.appointment-container').addClass('selected');
        $(newAppointmentElement).find('.appointment-toolbar').addClass('selected');
        $('.accordion-body').removeClass('selected');
        $(newAppointmentElement).find('.accordion-body').addClass('selected');

        $('.default-new-appt').addClass('hidden');

        updateAppointmentNumbers();
        console.log('New appointment inserted');
      },
      error: function (xhr, status, error) {
        toastr.error("You are not allowed to add new appointments.");
        console.error("Error adding new appointment:", error);
      }
    });
  });

  $(document).on('click', '.delete-appointment', function (event) {
    event.preventDefault();

    const $deleteButton = $(this);
    const $appointmentContainer = $deleteButton.closest('.appointment-container');
    const appointmentId = $deleteButton.data('appointmentId');

    // Check if appointment has completed treatments
    if ($appointmentContainer.find('#charted_treatment_completed').is(':checked')) {
      Swal.fire(
        'Error!',
        'Cannot remove appointment with completed treatments.',
        'error'
      );
      return;
    }

    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      reverseButtons: true
    }).then((result) => {
      if (!result.isConfirmed) return;

      $.ajax({
        type: 'DELETE',
        url: `/admin/patients/${window._patientId}/charting/delete_appointment?appointment_id=${appointmentId}`,
        contentType: 'application/json',
        dataType: 'json',
        headers: {
          'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (data) {
          if (data.success) {
            const $appointmentContainerToRemove = $(`.appointment-container[data-appointment-id="${appointmentId}"]`);
            const $treatmentPlanContainer = $appointmentContainerToRemove.closest('#treatment-plan-container');
            const $apptContainers = $treatmentPlanContainer.find('.appointment-container');
            const $newApptBlock = $appointmentContainerToRemove.parent().parent().find('#newappt');

            // Remove the entire wrapper that contains the appointment
            $appointmentContainerToRemove.parent().parent().remove();

            if ($('.appointment-block').length === 1) {
              $('.default-new-appt').removeClass('hidden');
            }

            // Remove any related treatment images if you have that function
            removeTreatmentImagesFromTeeth(data.deleted_treatments);

            Swal.fire(
              'Deleted!',
              'The appointment has been deleted.',
              'success'
            );
          } else {
            Swal.fire(
              'Error!',
              'Failed to delete the appointment. Please try again.',
              'error'
            );
          }
        },
        error: function (xhr, status, error) {
          console.error('Error:', error);
          Swal.fire(
            'Error!',
            'An error occurred while deleting the appointment. Please try again.',
            'error'
          );
        }
      });
    });
  });
});
