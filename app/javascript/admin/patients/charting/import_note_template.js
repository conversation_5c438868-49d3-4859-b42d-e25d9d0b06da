$(document).ready(function () {
  initializeTemplateModal();
});

function initializeTemplateModal() {
  let selectedTemplate = null;
  let currentChartedTreatmentId = null;

  $('.import-note-template-btn').on('click', function (e) {
    e.preventDefault();
    currentChartedTreatmentId = $(this).data('charted-treatment-id');
    openImportNoteTemplateModal();
  });

  function openImportNoteTemplateModal() {
    selectedTemplate = null;
    $('#importNoteTemplateModal').removeClass('hidden');
    $('#templatePreview').html('Select a template to preview its content');
    $('#importTemplateBtn').prop('disabled', true);
    $('.template-item').removeClass('border-blue-500 bg-blue-50');
  }

  window.closeImportNoteTemplateModal = function() {
    $('#importNoteTemplateModal').addClass('hidden');
    selectedTemplate = null;
    currentChartedTreatmentId = null;
  };

  window.selectTemplate = function(element) {
    $('.template-item').removeClass('border-blue-500 bg-blue-50');
    $(element).addClass('border-blue-500 bg-blue-50');

    selectedTemplate = {
      id: $(element).data('template-id'),
      content: $(element).data('template-content')
    };

    $('#templatePreview').html(selectedTemplate.content);
    $('#importTemplateBtn').prop('disabled', false);
  };

  $('#importTemplateBtn').on('click', function() {
    if (!selectedTemplate || !currentChartedTreatmentId) {
      return;
    }

    const editorId = `notes_${currentChartedTreatmentId}`;

    if (typeof tinymce !== 'undefined') {
      const editor = tinymce.get(editorId);

      if (editor) {
        const currentContent = editor.getContent();
        const newContent = currentContent ?
          currentContent + '<br><br>' + selectedTemplate.content :
          selectedTemplate.content;

        editor.setContent(newContent);

        if (typeof window.updateNote === 'function') {
          window.updateNote(currentChartedTreatmentId);
        }

        closeImportNoteTemplateModal();

        const toastContainer = $('.toast-container');
        if (toastContainer.length && typeof toastr !== 'undefined') {
          toastr.success('Note template imported successfully');
        }
      } else {
        const notesTextarea = $(`#${editorId}`);
        if (notesTextarea.length) {
          const currentNotes = notesTextarea.val();
          const newContent = currentNotes ?
            currentNotes + '\n\n' + selectedTemplate.content :
            selectedTemplate.content;

          notesTextarea.val(newContent);
          notesTextarea.trigger('input');

          closeImportNoteTemplateModal();

          const toastContainer = $('.toast-container');
          if (toastContainer.length && typeof toastr !== 'undefined') {
            toastr.success('Note template imported successfully');
          }
        }
      }
    } else {
      const notesTextarea = $(`#${editorId}`);
      if (notesTextarea.length) {
        const currentNotes = notesTextarea.val();
        const newContent = currentNotes ?
          currentNotes + '\n\n' + selectedTemplate.content :
          selectedTemplate.content;

        notesTextarea.val(newContent);
        notesTextarea.trigger('input');

        closeImportNoteTemplateModal();
      }
    }
  });

  $(document).on('keydown', function(e) {
    if (e.key === 'Escape' && !$('#importNoteTemplateModal').hasClass('hidden')) {
      closeImportNoteTemplateModal();
    }
  });

  $('#importNoteTemplateModal').on('click', function(e) {
    if (e.target === this) {
      closeImportNoteTemplateModal();
    }
  });
}

window.initializeTemplateModal = initializeTemplateModal;
