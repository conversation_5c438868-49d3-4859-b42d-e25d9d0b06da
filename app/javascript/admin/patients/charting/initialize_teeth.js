// =========================
// VARIABLE DECLARATIONS
// =========================
var selectedTooth = null;
var $clickedToothElement = null;

// Mapping of permanent teeth to their deciduous counterparts
var childToothMap = {
  // Upper Right Quadrant
  'UR1': 'URA',
  'UR1F': 'URAF',
  'UR2': 'URB',
  'UR2F': 'URBF',
  'UR3': 'URC',
  'UR3F': 'URCF',
  'UR4': 'URD',
  'UR4F': 'URDF',
  'UR5': 'URE',
  'UR5F': 'UREF',
  'UR6': 'URF',
  'UR6F': 'URFF',
  'UR7': 'URG',
  'UR7F': 'URGF',
  'UR8': 'URH',
  'UR8F': 'URHF',

  // Upper Left Quadrant
  'UL1': 'ULA',
  'UL1F': 'ULAF',
  'UL2': 'ULB',
  'UL2F': 'ULBF',
  'UL3': 'ULC',
  'UL3F': 'ULCF',
  'UL4': 'ULD',
  'UL4F': 'ULDF',
  'UL5': 'ULE',
  'UL5F': 'ULEF',
  'UL6': 'ULF',
  'UL6F': 'ULFF',
  'UL7': 'ULG',
  'UL7F': 'ULGF',
  'UL8': 'ULH',
  'UL8F': 'ULHF',

  // Lower Left Quadrant
  'LL1': 'LLA',
  'LL1F': 'LLAF',
  'LL2': 'LLB',
  'LL2F': 'LLBF',
  'LL3': 'LLC',
  'LL3F': 'LLCF',
  'LL4': 'LLD',
  'LL4F': 'LLDF',
  'LL5': 'LLE',
  'LL5F': 'LLEF',
  'LL6': 'LLF',
  'LL6F': 'LLFF',
  'LL7': 'LLG',
  'LL7F': 'LLGF',
  'LL8': 'LLH',
  'LL8F': 'LLHF',

  // Lower Right Quadrant
  'LR1': 'LRA',
  'LR1F': 'LRAF',
  'LR2': 'LRB',
  'LR2F': 'LRBF',
  'LR3': 'LRC',
  'LR3F': 'LRCF',
  'LR4': 'LRD',
  'LR4F': 'LRDF',
  'LR5': 'LRE',
  'LR5F': 'LREF',
  'LR6': 'LRF',
  'LR6F': 'LRFF',
  'LR7': 'LRG',
  'LR7F': 'LRGF',
  'LR8': 'LRH',
  'LR8F': 'LRHF'
};

// Create a reverse mapping from deciduous to permanent
var childToAdultMap = {};
$.each(childToothMap, function(adult, child) {
  childToAdultMap[child] = adult;
});

// Global treatment data storage for tooltips
var toothTreatmentData = {};

// Function to initialize tooth treatment data
function initializeToothTreatmentData() {
  toothTreatmentData = {};

  // Initialize all tooth positions
  var allTeethPositions = [
    'UR8', 'UR7', 'UR6', 'UR5', 'UR4', 'UR3', 'UR2', 'UR1',
    'UL1', 'UL2', 'UL3', 'UL4', 'UL5', 'UL6', 'UL7', 'UL8',
    'LL8', 'LL7', 'LL6', 'LL5', 'LL4', 'LL3', 'LL2', 'LL1',
    'LR1', 'LR2', 'LR3', 'LR4', 'LR5', 'LR6', 'LR7', 'LR8',
    // Child teeth
    'URA', 'URB', 'URC', 'URD', 'URE', 'URF', 'URG', 'URH',
    'ULA', 'ULB', 'ULC', 'ULD', 'ULE', 'ULF', 'ULG', 'ULH',
    'LLA', 'LLB', 'LLC', 'LLD', 'LLE', 'LLF', 'LLG', 'LLH',
    'LRA', 'LRB', 'LRC', 'LRD', 'LRE', 'LRF', 'LRG', 'LRH'
  ];

  allTeethPositions.forEach(function(toothPos) {
    toothTreatmentData[toothPos] = {
      planned: [],
      completed: []
    };
  });
}

// Function to add treatment to tooth data
function addTreatmentToToothData(toothPosition, treatmentInfo, isCompleted) {
  var cleanToothPos = toothPosition.replace('F', '');

  if (!toothTreatmentData[cleanToothPos]) {
    toothTreatmentData[cleanToothPos] = {
      planned: [],
      completed: []
    };
  }

  var treatmentData = {
    name: treatmentInfo.name || 'Unknown Treatment',
    surfaces: treatmentInfo.surfaces || '',
    code: treatmentInfo.code || '',
    appointmentDate: treatmentInfo.appointmentDate || '',
    practitioner: treatmentInfo.practitioner || ''
  };

  if (isCompleted) {
    toothTreatmentData[cleanToothPos].completed.push(treatmentData);
  } else {
    toothTreatmentData[cleanToothPos].planned.push(treatmentData);
  }
}

// Function to load tooth history data asynchronously
function loadToothHistoryForTooltip(toothPosition) {
  return new Promise(function(resolve, reject) {
    var courseOfTreatmentId = $(".course-of-treatment-item.sleected-cot").data('course-of-treatment-id');

    $.ajax({
      url: '/admin/patients/' + window._patientId + '/charting/load_tooth_history',
      type: 'GET',
      data: {
        tooth_position: toothPosition,
        course_of_treatment_id: courseOfTreatmentId
      },
      success: function(data) {
        resolve(data);
      },
      error: function(xhr, status, error) {
        reject(error);
      }
    });
  });
}

// Function to generate tooltip content from backend data
function generateToothTooltipFromBackend(data, toothPosition) {
  if (!data.treatments || data.treatments.length === 0) {
    return null;
  }

  var completedTreatments = data.treatments.filter(function(t) { return t.completed; });
  var plannedCurrentCotTreatments = data.treatments.filter(function(t) { return !t.completed && t.is_current_cot; });
  var plannedOtherCotTreatments = data.treatments.filter(function(t) { return !t.completed && !t.is_current_cot; });

  var html = '<div class="bg-card text-card-foreground shadow-lg rounded-lg" style="width: 400px;">';

  // Header
  html += '<div class="flex flex-col space-y-1.5 rounded-t-lg p-2">';
  html += '<h3 class="tracking-tight text-sm font-semibold text-gray-900">Tooth ' + toothPosition + '</h3>';
  html += '</div>';

  html += '<div class="p-2 space-y-1">';

  // Completed Treatments Section
  if (completedTreatments.length > 0) {
    html += '<div>';
    html += '<div class="flex items-center gap-1.5 mb-2">';
    html += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check h-3.5 w-3.5 text-green-500">';
    html += '<circle cx="12" cy="12" r="10"></circle>';
    html += '<path d="m9 12 2 2 4-4"></path>';
    html += '</svg>';
    html += '<h4 class="text-xs font-semibold text-gray-700">Completed</h4>';
    html += '</div>';
    html += '<ul class="space-y-1">';

    completedTreatments.forEach(function(treatment) {
      html += '<li class="flex items-start gap-2 justify-between mb-1">';
      html += '<div class="flex items-start gap-2">';

      // Avatar
      if (treatment.practitioner_avatar_url) {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="' + treatment.practitioner_name + '" src="' + treatment.practitioner_avatar_url + '">';
        html += '</span>';
      } else {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="User avatar" src="/placeholder.svg?height=20&width=20">';
        html += '</span>';
      }

      html += '<div>';
      html += '<p class="font-medium text-xs text-gray-800 leading-tight">' + treatment.treatment_name + '</p>';
      html += '<div class="flex items-baseline gap-1.5">';
      html += '<p class="text-[11px] text-gray-500 leading-tight">' + treatment.practitioner_name + '</p>';
      html += '<p class="text-[11px] !text-blue-600 font-medium leading-tight">' + treatment.completed_at + '</p>';
      html += '</div>';
      html += '</div>';
      html += '</div>';

      // Surface badge
      if (treatment.surface) {
        html += '<div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground mt-1 sm:mt-0 capitalize text-xs px-1.5 py-0.5">';
        html += treatment.surface;
        html += '</div>';
      }

      html += '</li>';
    });

    html += '</ul>';
    html += '</div>';
  }

  // Divider
  if (completedTreatments.length > 0 && (plannedCurrentCotTreatments.length > 0 || plannedOtherCotTreatments.length > 0)) {
    html += '<div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-px w-full my-1"></div>';
  }

  // Planned On This Course Of Treatment Section
  if (plannedCurrentCotTreatments.length > 0) {
    html += '<div>';
    html += '<div class="flex items-center gap-1.5 mb-2">';
    html += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-3.5 w-3.5 text-blue-500">';
    html += '<path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>';
    html += '<path d="M16 2v4"></path>';
    html += '<path d="M8 2v4"></path>';
    html += '<path d="M3 10h5"></path>';
    html += '<path d="M17.5 17.5 16 16.3V14"></path>';
    html += '<circle cx="16" cy="16" r="6"></circle>';
    html += '</svg>';
    html += '<h4 class="text-xs font-semibold text-gray-700">Planned On This Course Of Treatment</h4>';
    html += '</div>';
    html += '<ul class="space-y-1">';

    plannedCurrentCotTreatments.forEach(function(treatment) {
      html += '<li class="flex items-start gap-2 justify-between mb-1">';
      html += '<div class="flex items-start gap-2">';

      // Avatar
      if (treatment.practitioner_avatar_url) {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="' + treatment.practitioner_name + '" src="' + treatment.practitioner_avatar_url + '">';
        html += '</span>';
      } else {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="User avatar" src="/placeholder.svg?height=20&width=20">';
        html += '</span>';
      }

      html += '<div>';
      html += '<p class="font-medium text-xs text-gray-800 leading-tight">' + treatment.treatment_name + '</p>';
      html += '<div class="flex items-baseline gap-1.5">';
      html += '<p class="text-[11px] text-gray-500 leading-tight">' + treatment.practitioner_name + '</p>';
      html += '<p class="text-[11px] !text-blue-600 font-medium leading-tight">' + treatment.appointment_date + '</p>';
      html += '</div>';
      html += '</div>';
      html += '</div>';

      // Surface badge
      if (treatment.surface) {
        html += '<div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground mt-1 sm:mt-0 capitalize text-xs px-1.5 py-0.5">';
        html += treatment.surface;
        html += '</div>';
      }

      html += '</li>';
    });

    html += '</ul>';
    html += '</div>';
  }

  // Divider
  if (plannedCurrentCotTreatments.length > 0 && plannedOtherCotTreatments.length > 0) {
    html += '<div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-px w-full my-1"></div>';
  }

  // Planned On Another Course Of Treatment Section
  if (plannedOtherCotTreatments.length > 0) {
    html += '<div>';
    html += '<div class="flex items-center gap-1.5 mb-2">';
    html += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-3.5 w-3.5 text-orange-500">';
    html += '<path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>';
    html += '<path d="M16 2v4"></path>';
    html += '<path d="M8 2v4"></path>';
    html += '<path d="M3 10h5"></path>';
    html += '<path d="M17.5 17.5 16 16.3V14"></path>';
    html += '<circle cx="16" cy="16" r="6"></circle>';
    html += '</svg>';
    html += '<h4 class="text-xs font-semibold text-gray-700">Planned On Another Course Of Treatment</h4>';
    html += '</div>';
    html += '<ul class="space-y-1">';

    plannedOtherCotTreatments.forEach(function(treatment) {
      html += '<li class="flex items-start gap-2 justify-between mb-1">';
      html += '<div class="flex items-start gap-2">';

      // Avatar
      if (treatment.practitioner_avatar_url) {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="' + treatment.practitioner_name + '" src="' + treatment.practitioner_avatar_url + '">';
        html += '</span>';
      } else {
        html += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
        html += '<img class="aspect-square h-full w-full" alt="User avatar" src="/placeholder.svg?height=20&width=20">';
        html += '</span>';
      }

      html += '<div>';
      html += '<p class="font-medium text-xs text-gray-800 leading-tight">' + treatment.treatment_name + '</p>';
      html += '<div class="flex items-baseline gap-1.5">';
      html += '<p class="text-[11px] text-gray-500 leading-tight">' + treatment.practitioner_name + '</p>';
      html += '<p class="text-[11px] !text-blue-600 font-medium leading-tight">' + treatment.appointment_date + '</p>';
      html += '</div>';
      html += '</div>';
      html += '</div>';

      // Surface badge
      if (treatment.surface) {
        html += '<div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground mt-1 sm:mt-0 capitalize text-xs px-1.5 py-0.5">';
        html += treatment.surface;
        html += '</div>';
      }

      html += '</li>';
    });

    html += '</ul>';
    html += '</div>';
  }

  html += '</div>';
  html += '</div>';

  return html;
}

// Function to generate tooltip content for a tooth
function generateToothTooltipContent(toothPosition) {
  var cleanToothPos = toothPosition.replace('F', '');
  var data = toothTreatmentData[cleanToothPos];

  if (!data || (data.planned.length === 0 && data.completed.length === 0)) {
    return loadToothHistoryForTooltip(toothPosition).then(function(data) {
      return generateToothTooltipFromBackend(data, toothPosition);
    });
  }

  var content = '<div class="bg-card text-card-foreground shadow-lg rounded-lg" style="width: 400px;">';

  // Header
  content += '<div class="flex flex-col space-y-1.5 rounded-t-lg p-2">';
  content += '<h3 class="tracking-tight text-sm font-semibold text-gray-900">Tooth ' + cleanToothPos + '</h3>';
  content += '</div>';

  content += '<div class="p-2 space-y-1">';

  // Completed Treatments Section
  if (data.completed.length > 0) {
    content += '<div>';
    content += '<div class="flex items-center gap-1.5 mb-2">';
    content += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check h-3.5 w-3.5 text-green-500">';
    content += '<circle cx="12" cy="12" r="10"></circle>';
    content += '<path d="m9 12 2 2 4-4"></path>';
    content += '</svg>';
    content += '<h4 class="text-xs font-semibold text-gray-700">Completed</h4>';
    content += '</div>';
    content += '<ul class="space-y-1">';

    data.completed.forEach(function(treatment) {
      content += '<li class="flex items-start gap-2 justify-between mb-1">';
      content += '<div class="flex items-start gap-2">';

      // Default avatar (no user data available in cached data)
      content += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
      content += '<img class="aspect-square h-full w-full" alt="User avatar" src="/placeholder.svg?height=20&width=20">';
      content += '</span>';

      content += '<div>';
      content += '<p class="font-medium text-xs text-gray-800 leading-tight">' + treatment.name + '</p>';
      content += '<div class="flex items-baseline gap-1.5">';
      content += '<p class="text-[11px] text-gray-500 leading-tight">Unknown User</p>';
      content += '<p class="text-[11px] text-blue-600 font-medium leading-tight">-</p>';
      content += '</div>';
      content += '</div>';
      content += '</div>';

      // Surface badge
      if (treatment.surfaces) {
        content += '<div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground mt-1 sm:mt-0 capitalize text-xs px-1.5 py-0.5">';
        content += treatment.surfaces;
        content += '</div>';
      }

      content += '</li>';
    });

    content += '</ul>';
    content += '</div>';
  }

  // Divider
  if (data.completed.length > 0 && data.planned.length > 0) {
    content += '<div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-px w-full my-1"></div>';
  }

  // Planned Treatments Section (current COT assumed for cached data)
  if (data.planned.length > 0) {
    content += '<div>';
    content += '<div class="flex items-center gap-1.5 mb-2">';
    content += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-3.5 w-3.5 text-blue-500">';
    content += '<path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>';
    content += '<path d="M16 2v4"></path>';
    content += '<path d="M8 2v4"></path>';
    content += '<path d="M3 10h5"></path>';
    content += '<path d="M17.5 17.5 16 16.3V14"></path>';
    content += '<circle cx="16" cy="16" r="6"></circle>';
    content += '</svg>';
    content += '<h4 class="text-xs font-semibold text-gray-700">Planned</h4>';
    content += '</div>';
    content += '<ul class="space-y-1">';

    data.planned.forEach(function(treatment) {
      content += '<li class="flex items-start gap-2 justify-between mb-1">';
      content += '<div class="flex items-start gap-2">';

      // Default avatar (no user data available in cached data)
      content += '<span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">';
      content += '<img class="aspect-square h-full w-full" alt="User avatar" src="/placeholder.svg?height=20&width=20">';
      content += '</span>';

      content += '<div>';
      content += '<p class="font-medium text-xs text-gray-800 leading-tight">' + treatment.name + '</p>';
      content += '<div class="flex items-baseline gap-1.5">';
      content += '<p class="text-[11px] text-gray-500 leading-tight">Unknown User</p>';
      content += '<p class="text-[11px] text-blue-600 font-medium leading-tight">-</p>';
      content += '</div>';
      content += '</div>';
      content += '</div>';

      // Surface badge
      if (treatment.surfaces) {
        content += '<div class="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground mt-1 sm:mt-0 capitalize text-xs px-1.5 py-0.5">';
        content += treatment.surfaces;
        content += '</div>';
      }

      content += '</li>';
    });

    content += '</ul>';
    content += '</div>';
  }

  content += '</div>';
  content += '</div>';

  return content;
}

// Function to initialize tooth tooltips
function initializeToothTooltips() {
  // Add custom CSS to remove tippy padding
  if (!document.getElementById('tippy-custom-styles')) {
    var style = document.createElement('style');
    style.id = 'tippy-custom-styles';
    style.textContent = `
      .tippy-box[data-theme~='no-padding'] {
        padding: 0 !important;
        background: white !important;
        border: none !important;
        color: #1f2937 !important;
      }
      .tippy-box[data-theme~='no-padding'] .tippy-content {
        padding: 0 !important;
        color: #1f2937 !important;
      }
      .tippy-arrow {
        color: white !important;
      }
      .tippy-arrow::before {
        border-top-color: white !important;
        border-bottom-color: white !important;
        border-left-color: white !important;
        border-right-color: white !important;
      }
      .tippy-box[data-theme~='no-padding'] h3,
      .tippy-box[data-theme~='no-padding'] p,
      .tippy-box[data-theme~='no-padding'] span:not([class*="text-"]),
      .tippy-box[data-theme~='no-padding'] div:not([class*="text-"]) {
        color: inherit !important;
      }
      .tippy-box[data-theme~='no-padding'] .bg-border,
      .tippy-box[data-theme~='no-padding'] [data-orientation="horizontal"] {
        background-color: #e5e7eb !important;
      }
      .tippy-box[data-theme~='no-padding'] .border,
      .tippy-box[data-theme~='no-padding'] [class*="border"] {
        border-color: #e5e7eb !important;
      }
    `;
    document.head.appendChild(style);
  }

  // First, destroy any existing tooltips
  $('.tooth-container').each(function() {
    if (this._tippy) {
      this._tippy.destroy();
    }
  });

  // Initialize tippy tooltips
  if (typeof tippy !== 'undefined') {
    var elements = document.querySelectorAll('.tooth-container[data-tooth-position]');

    if (elements.length > 0) {
      tippy(elements, {
        allowHTML: true,
        theme: 'no-padding',
        placement: 'top',
        interactive: true,
        maxWidth: 420,
        content: 'Loading treatments...',
        onShow: function(instance) {
          // Don't show tooltip if user has selected a treatment
          if (window._selectedTreatment) {
            return false;
          }

          var reference = instance.reference;
          var toothPosition = reference.getAttribute('data-tooth-position');


          if (toothPosition) {
            // First check if we have cached data
            var cachedContent = generateToothTooltipContent(toothPosition);

            if (cachedContent && typeof cachedContent === 'string') {
              // We have cached content, use it immediately
              instance.setContent(cachedContent);
            } else if (cachedContent && typeof cachedContent.then === 'function') {
              // We have a promise, wait for it
              cachedContent.then(function(content) {
                if (content) {
                  instance.setContent(content);
                } else {
                  instance.setContent('Tooth ' + toothPosition + ' - No treatments');
                }
              }).catch(function(error) {
                instance.setContent('Tooth ' + toothPosition + ' - Error loading treatments');
              });
            } else {
              // No cached content, load directly
              loadToothHistoryForTooltip(toothPosition).then(function(data) {
                var content = generateToothTooltipFromBackend(data, toothPosition);
                if (content) {
                  instance.setContent(content);
                } else {
                  instance.setContent('Tooth ' + toothPosition + ' - No treatments');
                }
              }).catch(function(error) {
                instance.setContent('Tooth ' + toothPosition + ' - Error loading treatments');
              });
            }
          } else {
            instance.setContent('Unknown tooth');
          }
        },
        onHide: function(instance) {
        }
      });
    }
  } else {
    console.error('Tippy.js is not available!');
    // Fallback - use browser title attribute
    $('.tooth-container').each(function() {
      var toothPosition = $(this).data('tooth-position');
      if (toothPosition) {
        var content = generateToothTooltipContent(toothPosition);
        if (content) {
          this.title = content.replace(/<[^>]*>/g, ''); // Strip HTML for title
        } else {
          this.title = 'Tooth ' + toothPosition + ' - No treatments';
        }
      }
    });
  }
}

// Function to refresh tooth tooltips
function refreshToothTooltips() {
  // Remove existing tooltips
  $('.tooth-container').each(function() {
    if (this._tippy) {
      this._tippy.destroy();
    }
  });

  // Re-initialize tooltips
  initializeToothTooltips();
}

// =========================
// HELPER FUNCTIONS
// =========================

// Convert a permanent tooth class to its deciduous equivalent
function convertToDeciduous(tooth) {
  return childToothMap[tooth.toUpperCase()] || tooth;
}

// Convert a deciduous tooth class to its permanent equivalent
function convertToPermanent(tooth) {
  return childToAdultMap[tooth.toUpperCase()] || tooth;
}

// Retrieve CSRF token (assuming it's stored in a meta tag)
function getCsrfToken() {
  return $('meta[name="csrf-token"]').attr('content') || '';
}

// =========================
// MAIN FUNCTIONS
// =========================

// Apply condition to a tooth
function applyConditionToTooth(tooth, condition, save) {
  if (typeof save === 'undefined') save = true;

  var $toothElement = $('.' + tooth.toLowerCase());
  if ($toothElement.length === 0) {
    console.error('Tooth element with class ' + tooth.toLowerCase() + ' not found.');
    return;
  }

  switch (condition.toLowerCase()) {
    case 'missing':
      removeTooth(tooth);
      break;
    case 'deciduous':
      updateToothToDeciduous(tooth);
      break;
    case 'permanent':
      updateToothToPermanent(tooth);
      break;
    case 'mixed dentition':
      updateToothToMixed(tooth);
      break;
    case 'unwatch':
      save = false;
      unwatchTooth(tooth);
      break;
    default:
      overlayConditionImage(tooth, condition);
  }

  if (save) {
    saveConditionToUser(tooth, condition);
  }
}

// Remove "watch tooth" status
function unwatchTooth(tooth) {
  var userId = window._patientId;
  $.ajax({
    url: '/admin/users/remove_tooth_condition',
    type: 'POST',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    data: {
      tooth: tooth,
      user_id: userId,
    },
    success: function (response) {
      if (response.success) {
        var $toothElement = $('.teethgrid > div.' + tooth.toLowerCase() + 'f');
        var $images = $toothElement.find('.tooth-condition-img');
        var $filteredImages = $images.filter(function() {
          return decodeURIComponent($(this).attr('src')).includes('watch tooth');
        });

        if ($filteredImages.length) {
          $filteredImages.eq(0).remove();
        }
        toastr.success("Your tooth condition has been deleted");
      } else {
        toastr.error("Failed to delete tooth condition");
      }
    },
    error: function () {
      toastr.error("An error occurred while deleting the tooth condition");
    }
  });
}

// Removes a tooth visually
function removeTooth(tooth) {
  var teeth = [];
  if (!tooth.endsWith('F') && !tooth.endsWith('f')) {
    teeth = [tooth, tooth + 'F'];
  } else {
    teeth = [tooth, tooth.slice(0, -1)];
  }

  $.each(teeth, function(index, t) {
    var $toothElements = $('.' + t.toLowerCase());
    $toothElements.each(function() {
      var $el = $(this);
      // Hide all images
      $el.find('img').addClass('hidden');
      // Add missing-tooth indicator
      var $missingIndicator = $('<div></div>').addClass('missing-tooth-indicator');
      $el.append($missingIndicator);
    });
  });
}

// Update a tooth to deciduous
function updateToothToDeciduous(tooth) {
  var teeth = [tooth, tooth + 'F'];
  var $toothElement = (tooth.charAt(3) === 'F')
    ? $('.' + tooth.toLowerCase())
    : $('.' + tooth.toLowerCase() + 'f');
  var $mixedImage = $toothElement.find('.mixed-dentition');
  if ($mixedImage.length) {
    $mixedImage.remove();
  }

  $.each(teeth, function(i, t) {
    var deciduousTooth = convertToDeciduous(t);
    if (deciduousTooth !== t) {
      updateToothClassAndImage(t, deciduousTooth, 'https://upodmedican.b-cdn.net/deciduous/' + deciduousTooth + '.png');
      removeConditionOverlay(deciduousTooth);
    }
  });
}

// Update a tooth to permanent
function updateToothToPermanent(tooth) {
  var teeth = [tooth, tooth + 'F'];
  var $toothElement = (tooth.charAt(3) === 'F')
    ? $('.' + tooth.toLowerCase())
    : $('.' + (tooth + 'F').toLowerCase());
  var $mixedImage = $toothElement.find('.mixed-dentition');
  if ($mixedImage.length) {
    $mixedImage.remove();
  }

  $.each(teeth, function(i, t) {
    var permanentTooth = convertToPermanent(t);
    if (permanentTooth !== t) {
      updateToothClassAndImage(t, permanentTooth, 'https://upodmedican.b-cdn.net/Adult Teeth Individual Final/' + permanentTooth + '.png');
      removeConditionOverlay(permanentTooth);
    }
  });
}

// Update class name and image of a tooth
function updateToothClassAndImage(oldClass, newClass, imageUrl) {
  var $toothElement = $('.' + oldClass.toLowerCase());
  if ($toothElement.length === 0) {
    console.error('Tooth element with class ' + oldClass.toLowerCase() + ' not found.');
    return;
  }

  // Remove old class, add new class
  $toothElement.removeClass(oldClass.toLowerCase()).addClass(newClass.toLowerCase());

  // Ensure position-relative class
  if (!$toothElement.hasClass('position-relative')) {
    $toothElement.addClass('position-relative');
  }

  // Check existing main tooth image
  var $toothImg = $toothElement.find('img:not(.tooth-condition-img)');
  if ($toothImg.length) {
    $toothImg.attr('src', imageUrl);
  } else {
    var $img = $('<img>')
      .attr('src', imageUrl)
      .addClass('img-fluid');
    $toothElement.prepend($img);
  }
}

// Mark tooth as mixed dentition
function updateToothToMixed(tooth) {
  var $toothElement = (tooth.charAt(3) === 'F')
    ? $('.' + tooth.toLowerCase())
    : $('.' + tooth.toLowerCase() + 'f');
  var $mixedImage = $toothElement.find('.mixed-dentition');

  // If it's already there, do nothing (or remove if you prefer)
  if (!$mixedImage.length) {
    var thirdChar = tooth.charAt(2);
    if (thirdChar >= '1' && thirdChar <= '5') {
      // Convert to deciduous
      var deciduousTooth = convertToDeciduous(tooth);
      addToothImage(tooth, deciduousTooth, 'https://upodmedican.b-cdn.net/deciduous/' + deciduousTooth + (tooth.endsWith('F') ? '' : 'F') + '.png');
    } else if (thirdChar >= 'A' && thirdChar <= 'E') {
      // Convert to permanent
      var permanentTooth = convertToPermanent(tooth);
      addToothImage(tooth, permanentTooth, 'https://upodmedican.b-cdn.net/Adult Teeth Individual Final/' + permanentTooth + (tooth.endsWith('F') ? '' : 'F') + '.png');
    }
  }
}

// Add a tooth image overlay (for mixed)
function addToothImage(oldClass, newClass, imageUrl) {
  var $toothElement;
  if (oldClass.endsWith('F')) {
    $toothElement = $('.' + oldClass.toLowerCase());
  } else {
    $toothElement = $('.' + (oldClass + 'F').toLowerCase());
  }

  if ($toothElement.length === 0) {
    console.error('Tooth element with class ' + oldClass.toLowerCase() + ' not found.');
    return;
  }

  var $deciduousClone = $toothElement.clone();
  var $toothImg = $deciduousClone.find('img:not(.tooth-condition-img)');
  $toothImg
    .attr('src', imageUrl)
    .addClass('mixed-dentition tooth-condition-img')
    .css({
      zIndex: 100,
      width: '60%',
      position: 'absolute'
    });

  // Positioning
  if (oldClass.startsWith('UR')) {
    $toothImg.css({ bottom: '55%', right: '70%', float: 'right' });
  } else if (oldClass.startsWith('UL')) {
    $toothImg.css({ bottom: '55%', left: '70%', float: 'left' });
  } else if (oldClass.startsWith('LR')) {
    $toothImg.css({ top: '50%', right: '60%', float: 'right' });
  } else {
    $toothImg.css({ top: '60%', left: '50%', float: 'left' });
  }

  // Append the new image
  $toothElement.append($toothImg);
}

// Click handler for changing teeth on grid
function changeTeethOnGrid(target) {
  var $target = $(target);
  var imgSrc = $target.attr('src');
  var tooth = imgSrc.split('/').pop().split('.')[0];
  var thirdChar = tooth.charAt(2);

  // For permanent => change to deciduous
  if (/\d/.test(thirdChar)) {
    var teeth = [tooth, tooth.slice(0, -1)];
    var deciduousTooth = convertToDeciduous(tooth);

    $.each(teeth, function(i, t) {
      var dt = convertToDeciduous(t);
      if (dt !== t) {
        var $toothElement = $('.' + dt.toLowerCase());
        if ($toothElement.length === 0) return;

        $toothElement.find('.tooth-treatment-img').remove();
        updateToothClassAndImage(dt, t, 'https://upodmedican.b-cdn.net/Adult Teeth Individual Final/' + t + '.png');
      }
    });

    // Reload treatments for each appointment in the plan
    $('#treatment-plan-container .appointment-container').each(function () {
      var appointmentId = $(this).data('appointment-id');
      $.ajax({
        url: '/admin/teeth/load_treatment',
        type: 'POST',
        data: { appointmentId: appointmentId },
        headers: { 'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content') },
        success: function (treatmentResponse) {
          if (treatmentResponse.success) {
            var teethOrder = ['UR8','UR7','UR6','UR5','UR4','UR3','UR2','UR1',
              'UL1','UL2','UL3','UL4','UL5','UL6','UL7','UL8',
              'LL8','LL7','LL6','LL5','LL4','LL3','LL2','LL1',
              'LR1','LR2','LR3','LR4','LR5','LR6','LR7','LR8'];

            // Filter the treatments for this tooth
            var filteredTreatments = $.grep(treatmentResponse.treatments, function(el) {
              var toothName = tooth.slice(0, -1);
              var elTooth = el[0];
              if (elTooth.includes(' - ')) {
                var parts = elTooth.split(' - ');
                var startTooth = $.trim(parts[0]);
                var endTooth   = $.trim(parts[1]);
                var startIndex = teethOrder.indexOf(startTooth);
                var endIndex   = teethOrder.indexOf(endTooth);
                var toothIndex = teethOrder.indexOf(toothName);

                if (startIndex === -1 || endIndex === -1 || toothIndex === -1) {
                  return false;
                }
                var minIndex = Math.min(startIndex, endIndex);
                var maxIndex = Math.max(startIndex, endIndex);
                return toothIndex >= minIndex && toothIndex <= maxIndex;
              } else {
                return elTooth === toothName;
              }
            });

            // Re-apply each treatment
            $.each(filteredTreatments, function(idx, treatment) {
              var toothPosition = treatment[0];
              var treatmentId = treatment[1];
              var isFullToothTreatment = treatment[3];
              var isComplete = treatment[5];
              var ctid = treatment[7];
              var $treatmentElement = $('.treatment[data-treatment-id="' + treatmentId + '"]');

              if ($treatmentElement.length > 0) {
                var folder = ($treatmentElement.data('treatment-folder') || '').trim();
                var selectedTreatment = folder ? folder.toLowerCase() : $treatmentElement.text().trim().toLowerCase();

                if (toothPosition.includes(' - ')) {
                  var parts = toothPosition.split(' - ');
                  var startTooth = $.trim(parts[0]);
                  var endTooth   = $.trim(parts[1]);
                  var startIndex = teethOrder.indexOf(startTooth);
                  var endIndex   = teethOrder.indexOf(endTooth);

                  if (startIndex !== -1 && endIndex !== -1) {
                    var minIndex = Math.min(startIndex, endIndex);
                    var maxIndex = Math.max(startIndex, endIndex);

                    for (var i = minIndex; i <= maxIndex; i++) {
                      var tth = teethOrder[i];
                      applyTreatmentToTooth(tth, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                      applyTreatmentToTooth(tth + 'F', selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                    }
                  }
                } else {
                  applyTreatmentToTooth(toothPosition, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                  applyTreatmentToTooth(toothPosition + 'F', selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                }
              }
            });
          } else {
            console.error('Failed to load treatments:', treatmentResponse.error);
            Swal.fire("Error", "Failed to load treatments", "error");
          }
        },
        error: function (xhr, status, error) {
          console.error('Error loading treatments:', error);
          Swal.fire("Error", "An error occurred while loading treatments", "error");
        }
      });
    });

    $target.remove();
    addToothImage(tooth, deciduousTooth, 'https://upodmedican.b-cdn.net/deciduous/' + deciduousTooth + (tooth.endsWith('F') ? '' : 'F') + '.png');
  }
  else {
    // For deciduous => change to permanent
    var teeth2 = [tooth, tooth.slice(0, -1)];
    var permanentTooth = convertToPermanent(tooth);

    $.each(teeth2, function(i, t) {
      var pt = convertToPermanent(t);
      if (pt !== t) {
        var $toothElement = $('.' + pt.toLowerCase());
        if ($toothElement.length === 0) return;

        $toothElement.find('.tooth-treatment-img').remove();
        updateToothClassAndImage(pt, t, 'https://upodmedican.b-cdn.net/deciduous/' + t + '.png');
      }
    });

    $('#treatment-plan-container .appointment-container').each(function () {
      var appointmentId = $(this).data('appointment-id');
      $.ajax({
        url: '/admin/teeth/load_treatment',
        type: 'POST',
        data: { appointmentId: appointmentId },
        headers: { 'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content') },
        success: function (treatmentResponse) {
          if (treatmentResponse.success) {
            var filteredTreatments = $.grep(treatmentResponse.treatments, function(el) {
              return el[0] === tooth.slice(0, -1);
            });
            $.each(filteredTreatments, function(idx, treatment) {
              var toothPosition = treatment[0];
              var treatmentId = treatment[1];
              var isFullToothTreatment = treatment[3];
              var isComplete = treatment[5];
              var ctid = treatment[7];
              var $treatmentElement = $('.treatment[data-treatment-id="' + treatmentId + '"]');

              if ($treatmentElement.length > 0) {
                var folder = ($treatmentElement.data('treatment-folder') || '').trim();
                var selectedTreatment = folder ? folder.toLowerCase() : $treatmentElement.text().trim().toLowerCase();

                applyTreatmentToTooth(toothPosition, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                applyTreatmentToTooth(toothPosition + 'F', selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
              }
            });
          } else {
            console.error('Failed to load treatments:', treatmentResponse.error);
            Swal.fire("Error", "Failed to load treatments", "error");
          }
        },
        error: function (xhr, status, error) {
          console.error('Error loading treatments:', error);
          Swal.fire("Error", "An error occurred while loading treatments", "error");
        }
      });
    });

    $target.remove();
    addToothImage(tooth, permanentTooth, 'https://upodmedican.b-cdn.net/Adult Teeth Individual Final/' + permanentTooth + (tooth.endsWith('F') ? '' : 'F') + '.png');
  }
}

// Used when re-applying treatments in the mixed-dentition changes
function applyTreatmentToTooth(toothPosition, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete) {
  var $treatment = $('.treatment[data-treatment-id="' + treatmentId + '"]');
  var region = $treatment.data('region');
  if (region === 'Patient') {
    return;
  }

  // Collect treatment data for tooltip
  var treatmentInfo = {
    name: $treatment.text().trim() || selectedTreatment || 'Unknown Treatment',
    surfaces: isFullToothTreatment ? 'Full tooth' : (treatment[2] || ''),
    code: $treatment.data('treatment-code') || '',
    appointmentDate: '',
    practitioner: ''
  };

  // Add treatment data to our global storage
  addTreatmentToToothData(toothPosition, treatmentInfo, isComplete);

  var $toothElement = $('.teethgrid > div.' + toothPosition.toLowerCase());
  if ($toothElement.length > 0) {
    if (isFullToothTreatment) {
      var imageName = selectedTreatment + '/' + toothPosition + '.png';
      var imageTag  = '<img src="https://upodmedican.b-cdn.net/' + imageName + '" '
        + 'class="img-fluid absolute ctid-' + ctid
        + ' treatment-id-' + treatmentId
        + ' tooth-treatment-img ' + (isComplete ? 'completed-treatment' : '')
        + '" style="left:0; z-index: 9; top:0;">';

      if ($toothElement.find('img[src="https://upodmedican.b-cdn.net/' + imageName + '"].treatment-id-' + treatmentId).length === 0 || isComplete) {
        $toothElement.append(imageTag);
      }
    } else {
      var surface = treatment[2];
      var imageNameSurface = selectedTreatment + '/' + toothPosition + '_' + surface + '.png';
      var imageTagSurface  = '<img src="https://upodmedican.b-cdn.net/' + imageNameSurface + '" '
        + 'class="img-fluid absolute ctid-' + ctid
        + ' treatment-id-' + treatmentId
        + ' tooth-treatment-img ' + (isComplete ? 'completed-treatment' : '')
        + '" style="left:0; z-index: 9; top:0;">';
      $toothElement.append(imageTagSurface);
    }
  }

  // Refresh tooltips after adding treatment
  refreshToothTooltips();
}

// Overlay a condition image on the tooth
function overlayConditionImage(tooth, condition) {
  var $toothElement = $('.' + tooth.toLowerCase());
  var $toothElementFront = $('.' + tooth.toLowerCase() + 'f');
  if ($toothElement.length === 0) {
    console.error('Tooth element with class ' + tooth.toLowerCase() + ' not found.');
    return;
  }

  removeConditionOverlay(tooth);

  var conditionImageUrl = 'https://upodmedican.b-cdn.net/' + condition + '/' + tooth.toUpperCase() + 'F.png';

  var $conditionImg = $('<img>')
    .attr('src', conditionImageUrl)
    .addClass('img-fluid absolute tooth-condition-img')
    .css({
      left: '0',
      top: '0',
      zIndex: '999'
    });

  if (tooth.charAt(3) === 'F') {
    $toothElement.append($conditionImg);
  } else {
    $toothElementFront.append($conditionImg);
  }
}

// Remove any condition overlay on a tooth
function removeConditionOverlay(tooth) {
  var $toothElement = $('.' + tooth.toLowerCase());
  if ($toothElement.length === 0) return;
  $toothElement.find('.tooth-treatment-img').remove();
}

// Save the condition to the user's record via an API call
function saveConditionToUser(tooth, condition) {
  var userId = window._patientId; // or dynamic retrieval
  $.ajax({
    url: '/admin/users/save_tooth_condition',
    type: 'POST',
    headers: {
      'X-CSRF-Token': getCsrfToken()
    },
    contentType: 'application/json',
    dataType: 'json',
    data: JSON.stringify({
      user_id: userId,
      tooth: tooth,
      condition: condition
    }),
    success: function(data) {
      console.log('Condition saved:', data);
    },
    error: function(xhr, status, error) {
      console.error('Error saving condition:', error);
    }
  });
}

// =========================
// JQUERY DOM READY
// =========================
$(document).ready(function() {

  initializeToothTreatmentData();

  // =========================
  // CLICK HANDLER FOR .treatmentcard
  // =========================
  $('.treatmentcard').on('click', function() {
    if ($(this).data('option') === 'reset-tooth') {
      // removeTreatmentFromToothSurface is not defined in snippet,
      // but we call it if it exists:
      if (typeof removeTreatmentFromToothSurface === 'function') {
        removeTreatmentFromToothSurface(selectedTooth, null);
      }
      return;
    }

    var condition = $(this).data('condition');
    if (selectedTooth && condition) {
      applyConditionToTooth(selectedTooth, condition.toLowerCase());
      if (condition.toLowerCase() === 'missing') {
        // If insertMissingToothToBaseCharting exists, call it
        if (typeof insertMissingToothToBaseCharting === 'function') {
          insertMissingToothToBaseCharting(selectedTooth);
        }
      }
    }
  });

  // Right-click on a tooth (both tooth level and surface level)
  $('.teethgrid > div').on('contextmenu', '.tooth-svg-overlay path, .tooth-svg-overlay polygon, .tooth-svg-overlay rect, .tooth-svg-overlay', function(e) {
    e.preventDefault();
    e.stopPropagation();

    var $customMenu = $('#toothContextMenu');
    var $unwatchBlock = $customMenu.find('#unwatch-option').closest('.col-3.p-0');
    var $watchBlock   = $customMenu.find('#watch-option').closest('.col-3.p-0');

    $clickedToothElement = $(this);

    const $toothDiv = $(this).closest('.teethgrid > div');
    if ($(this).hasClass('tooth-svg-overlay') || $(this).parents('.tooth-svg-overlay').length > 0) {
      $clickedToothElement = $(this);
    } else {
      $clickedToothElement = $toothDiv;
    }

    // Identify the tooth class (excluding position-relative)
    var classes = $toothDiv.attr('class').split(/\s+/);
    var toothClass = null;
    $.each(classes, function(i, cls) {
      if (cls !== 'position-relative' && cls !== 'tooth-container') {
        toothClass = cls;
        return false;
      }
    });
    selectedTooth = toothClass ? toothClass.toUpperCase() : null;

    if (!selectedTooth) {
      console.error('No valid tooth class found.');
      return false;
    }

    var decodedSrc = decodeURIComponent($toothDiv.find('.tooth-condition-img').attr('src') || '');
    if (decodedSrc.includes('watch tooth')) {
      $unwatchBlock.show();
      $watchBlock.hide();
    } else {
      $watchBlock.show();
      $unwatchBlock.hide();
    }

    $customMenu.removeClass('hidden').css({
      left: e.pageX,
      top: e.pageY
    });

    window.contextMenuVisible = true;

    loadFavoriteTreatments();

    // Set up click handler for favorite treatment items using event delegation
    $customMenu.off('click', '.favorite-treatment-item').on('click', '.favorite-treatment-item', function(evt) {
      evt.preventDefault();
      $customMenu.addClass('hidden');

      const treatmentId = $(this).data('treatment-id');
      const treatmentName = $(this).find('.font-medium').text();

      // Find the treatment in the sidebar
      const $treatmentElement = $(`.treatment[data-treatment-id="${treatmentId}"]`);
      if ($treatmentElement.length > 0) {
        // Use the proper treatment selection function
        selectTreatment($treatmentElement);
      }
    });

    // Set up the "View Tooth History" click handler
    $customMenu.find('#viewToothHistory').off('click').on('click', function(evt) {
      evt.preventDefault();
      $customMenu.addClass('hidden');

      // Use the tooth position from our data attribute
      var toothPosition = selectedTooth.substr(0, 3).toUpperCase();

      // Load tooth history via AJAX
      $.ajax({
        url: '/admin/patients/' + window._patientId + '/charting/load_tooth_history',
        type: 'GET',
        data: { tooth_position: toothPosition },
        success: function(data) {
          var $modal = $('#toothHistoryModal');
          var $modalTitle = $('#toothHistoryTitle');
          var $modalBody = $('#toothHistoryContent');

          $modalTitle.text(toothPosition);

          if (data.treatments && data.treatments.length > 0) {
            var historyHtml = '<div class="space-y-4">';
            data.treatments.forEach(function(treatment) {
              var statusBadge = treatment.completed ?
                '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>' :
                '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Planned</span>';

              historyHtml += '<div class="border border-gray-200 rounded-lg p-4">';
              historyHtml += '<div class="flex justify-between items-start mb-2">';
              historyHtml += '<h4 class="text-sm font-medium text-gray-900">' + treatment.treatment_name + '</h4>';
              historyHtml += statusBadge;
              historyHtml += '</div>';
              historyHtml += '<p class="text-sm text-gray-600 mb-2">' + treatment.practitioner_name + '</p>';
              historyHtml += '<div class="text-xs text-gray-500 space-y-1">';
              historyHtml += '<div>Practitioner: ' + treatment.practitioner_name + '</div>';
              historyHtml += '<div>Date: ' + treatment.formatted_date + '</div>';
              if (treatment.surface) {
                historyHtml += '<div>Surface: ' + treatment.surface + '</div>';
              }
              historyHtml += '</div>';
              historyHtml += '</div>';
            });
            historyHtml += '</div>';
            $modalBody.html(historyHtml);
          } else {
            $modalBody.html('<p class="text-gray-500 text-center py-4">No treatment history found for this tooth.</p>');
          }

          $modal.removeClass('hidden').attr('aria-hidden', 'false');
        },
        error: function(xhr, status, error) {
          console.error('Failed to load tooth history:', error);
          toastr.error('Failed to load tooth history');
        }
      });
    });

    // Set up the "Close" button click handler
    $customMenu.find('#closeContextMenu').off('click').on('click', function(evt) {
      evt.preventDefault();
      $customMenu.addClass('hidden');
    });

    // Set up the "Reset Tooth" click handler
    $customMenu.find('#resetTooth').off('click').on('click', function(evt) {
      evt.preventDefault();
      $customMenu.addClass('hidden');

      var toothPosition = selectedTooth.substr(0, 3).toUpperCase();

      Swal.fire({
        title: 'Reset Tooth',
        text: 'Are you sure you want to reset all treatments on this tooth?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, reset it',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: '/admin/patients/' + window._patientId + '/charting/reset_tooth',
            type: 'DELETE',
            data: {
              tooth_position: toothPosition
            },
            headers: {
              'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(data) {
              toastr.success('Tooth reset successfully');
              location.reload(); // Reload to refresh the display
            },
            error: function(xhr, status, error) {
              console.error('Failed to reset tooth:', error);

              var errorMessage = 'Failed to reset tooth. Please try again.';
              if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
              }

              toastr.error(errorMessage);
            }
          });
        }
      });
    });

    return false; // Prevent default context menu
  });

  // Fallback handler for right-clicks directly on tooth div (not on surfaces)
  $('.teethgrid > div').on('contextmenu', function(e) {
    // Only handle if the click wasn't on a surface element
    if ($(e.target).closest('.tooth-svg-overlay').length === 0) {
      e.preventDefault();

      var $customMenu = $('#toothContextMenu');
      var $unwatchBlock = $customMenu.find('#unwatch-option').closest('.col-3.p-0');
      var $watchBlock   = $customMenu.find('#watch-option').closest('.col-3.p-0');

      // Store reference to the clicked tooth element for treatment application
      $clickedToothElement = $(this);

      // Identify the tooth class (excluding position-relative)
      var classes = $(this).attr('class').split(/\s+/);
      var toothClass = null;
      $.each(classes, function(i, cls) {
        if (cls !== 'position-relative' && cls !== 'tooth-container') {
          toothClass = cls;
          return false;
        }
      });
      selectedTooth = toothClass ? toothClass.toUpperCase() : null;

      if (!selectedTooth) {
        console.error('No valid tooth class found.');
        return false;
      }

      var decodedSrc = decodeURIComponent($(this).find('.tooth-condition-img').attr('src') || '');
      if (decodedSrc.includes('watch tooth')) {
        $unwatchBlock.show();
        $watchBlock.hide();
      } else {
        $watchBlock.show();
        $unwatchBlock.hide();
      }

      $customMenu.removeClass('hidden').css({
        left: e.pageX,
        top: e.pageY
      });

      window.contextMenuVisible = true;

      loadFavoriteTreatments();

      // Set up click handler for favorite treatment items using event delegation
      $customMenu.off('click', '.favorite-treatment-item').on('click', '.favorite-treatment-item', function(evt) {
        evt.preventDefault();
        $customMenu.addClass('hidden');

        const treatmentId = $(this).data('treatment-id');
        const treatmentName = $(this).find('.font-medium').text();

        // Find the treatment in the sidebar
        const $treatmentElement = $(`.treatment[data-treatment-id="${treatmentId}"]`);
        if ($treatmentElement.length > 0) {
          // Use the proper treatment selection function
          selectTreatment($treatmentElement);
        }
      });

      // Set up the "View Tooth History" click handler
      $customMenu.find('#viewToothHistory').off('click').on('click', function(evt) {
        evt.preventDefault();
        $customMenu.addClass('hidden');

        // Use the tooth position from our data attribute
        var toothPosition = selectedTooth.substr(0, 3).toUpperCase();

        // Load tooth history via AJAX
        $.ajax({
          url: '/admin/patients/' + window._patientId + '/charting/load_tooth_history',
          type: 'GET',
          data: { tooth_position: toothPosition },
          success: function(data) {
            var $modal = $('#toothHistoryModal');
            var $modalTitle = $('#toothHistoryTitle');
            var $modalBody = $('#toothHistoryContent');

            $modalTitle.text(toothPosition);

            if (data.treatments && data.treatments.length > 0) {
              var content = '<div class="space-y-4">';
              data.treatments.forEach(function(treatment) {
                var completedBadge = treatment.is_completed ?
                  '<span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Completed</span>' :
                  '<span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Planned</span>';

                content += `
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                      <h4 class="font-semibold text-gray-900">${treatment.course_of_treatment_name || 'Treatment'}</h4>
                      ${completedBadge}
                    </div>
                    <p class="text-sm text-gray-600 mb-2">${treatment.treatment_name || 'No description'}</p>
                    <div class="text-xs text-gray-500 space-y-1">
                      <div>Practitioner: ${treatment.practitioner_name || 'Not assigned'}</div>
                      <div>Date: ${treatment.formatted_date || 'Not scheduled'}</div>
                      ${treatment.surface ? `<div>Surface: ${treatment.surface}</div>` : ''}
                    </div>
                  </div>
                `;
              });
              content += '</div>';
              $modalBody.html(content);
            } else {
              $modalBody.html('<p class="text-gray-500 text-center py-4">No treatment history found for this tooth.</p>');
            }

            $modal.removeClass('hidden');
          },
          error: function(xhr, status, error) {
            console.error('Failed to load tooth history:', error);
            toastr.error('Failed to load tooth history');
          }
        });
      });

      // Set up the "Close" button click handler
      $customMenu.find('#closeContextMenu').off('click').on('click', function(evt) {
        evt.preventDefault();
        $customMenu.addClass('hidden');
      });

      // Set up the "Reset Tooth" click handler
      $customMenu.find('#resetTooth').off('click').on('click', function(evt) {
        evt.preventDefault();
        $customMenu.addClass('hidden');

        var toothPosition = selectedTooth.substr(0, 3).toUpperCase();

        Swal.fire({
          title: 'Reset Tooth',
          text: 'Are you sure you want to reset all treatments on this tooth?',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#dc2626',
          cancelButtonColor: '#6b7280',
          confirmButtonText: 'Yes, reset it',
          cancelButtonText: 'Cancel'
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              url: '/admin/patients/' + window._patientId + '/charting/reset_tooth',
              type: 'DELETE',
              data: {
                tooth_position: toothPosition
              },
              headers: {
                'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
              },
              success: function(data) {
                toastr.success('Tooth reset successfully');
                location.reload(); // Reload to refresh the display
              },
              error: function(xhr, status, error) {
                console.error('Failed to reset tooth:', error);

                var errorMessage = 'Failed to reset tooth. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                  errorMessage = xhr.responseJSON.error;
                }

                toastr.error(errorMessage);
              }
            });
          }
        });
      });

      return false; // Prevent default context menu
    }
  });

  // =========================
  // INITIAL LOAD: APPLY CONDITIONS FROM DATABASE
  // =========================

  if ($('.charting').length === 0) {
    return
  }

  $.ajax({
    url: '/admin/users/' + window._patientId + '/tooth_conditions',
    type: 'GET',
    dataType: 'json',
    headers: {
      'X-CSRF-Token': getCsrfToken()
    },
    success: function(data) {
      var conditions = data.tooth_conditions;
      if (conditions) {
        $.each(conditions, function(tooth, condition) {
          applyConditionToTooth(tooth, condition);
        });
      }
    },
    error: function(xhr, status, error) {
      console.error('Error fetching initial tooth conditions:', error);
    }
  });

  initializeToothTooltips();

  // Reload treatments for each appointment in the plan
  $('#treatment-plan-container .appointment-container').each(function () {
    var appointmentId = $(this).data('appointment-id');
    $.ajax({
      url: '/admin/teeth/load_treatment',
      type: 'POST',
      data: { appointmentId: appointmentId },
      headers: { 'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content') },
      success: function (treatmentResponse) {
        if (treatmentResponse.success) {
          var teethOrder = ['UR8','UR7','UR6','UR5','UR4','UR3','UR2','UR1',
            'UL1','UL2','UL3','UL4','UL5','UL6','UL7','UL8',
            'LL8','LL7','LL6','LL5','LL4','LL3','LL2','LL1',
            'LR1','LR2','LR3','LR4','LR5','LR6','LR7','LR8'];

          // Filter the treatments for this tooth
          var filteredTreatments = $.grep(treatmentResponse.treatments, function(el) {
            var toothName = el[0];
            var elTooth = el[0];
            if (elTooth.includes(' - ')) {
              var parts = elTooth.split(' - ');
              var startTooth = $.trim(parts[0]);
              var endTooth   = $.trim(parts[1]);
              var startIndex = teethOrder.indexOf(startTooth);
              var endIndex   = teethOrder.indexOf(endTooth);
              var toothIndex = teethOrder.indexOf(toothName);

              if (startIndex === -1 || endIndex === -1 || toothIndex === -1) {
                return false;
              }
              var minIndex = Math.min(startIndex, endIndex);
              var maxIndex = Math.max(startIndex, endIndex);
              return toothIndex >= minIndex && toothIndex <= maxIndex;
            } else {
              return elTooth === toothName;
            }
          });

          // Re-apply each treatment
          $.each(filteredTreatments, function(idx, treatment) {
            var toothPosition = treatment[0];
            var treatmentId = treatment[1];
            var isFullToothTreatment = treatment[3];
            var isComplete = treatment[5];
            var ctid = treatment[7];
            var $treatmentElement = $('.treatment[data-treatment-id="' + treatmentId + '"]');

            if ($treatmentElement.length > 0) {
              var folder = ($treatmentElement.data('treatment-folder') || '').trim();
              var selectedTreatment = folder ? folder.toLowerCase() : $treatmentElement.text().trim().toLowerCase();

              if (toothPosition.includes(' - ')) {
                var parts = toothPosition.split(' - ');
                var startTooth = $.trim(parts[0]);
                var endTooth   = $.trim(parts[1]);
                var startIndex = teethOrder.indexOf(startTooth);
                var endIndex   = teethOrder.indexOf(endTooth);

                if (startIndex !== -1 && endIndex !== -1) {
                  var minIndex = Math.min(startIndex, endIndex);
                  var maxIndex = Math.max(startIndex, endIndex);

                  for (var i = minIndex; i <= maxIndex; i++) {
                    var tth = teethOrder[i];
                    applyTreatmentToTooth(tth, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                    applyTreatmentToTooth(tth + 'F', selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                  }
                }
              } else {
                applyTreatmentToTooth(toothPosition, selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
                applyTreatmentToTooth(toothPosition + 'F', selectedTreatment, isFullToothTreatment, treatment, ctid, treatmentId, isComplete);
              }
            }
          });
        } else {
          console.error('Failed to load treatments:', treatmentResponse.error);
          Swal.fire("Error", "Failed to load treatments", "error");
        }
      },
      error: function (xhr, status, error) {
        console.error('Error loading treatments:', error);
        Swal.fire("Error", "An error occurred while loading treatments", "error");
      },
      complete: function() {
        refreshToothTooltips();
      }
    });
  });

  // Refresh tooltips after all treatments are loaded
  refreshToothTooltips();

  // Also initialize tooltips after a small delay to ensure DOM is ready
  setTimeout(function() {
    initializeToothTooltips();
  }, 1000);

});

function loadFavoriteTreatments() {
  const $favoriteTreatmentsList = $('#favoriteTreatmentsList');

  $favoriteTreatmentsList.empty();

  const $favoriteTreatments = $('.favorite-treatments .treatment-container');

  if ($favoriteTreatments.length === 0) {

    const $allTreatments = $('.treatment-container');

    const $favoritedTreatments = $('.treatment-container').filter(function() {
      const $btn = $(this).find('.favorite-btn');
      const isFavorited = $btn.data('favorited') === true || $btn.data('favorited') === 'true';
      return isFavorited;
    });


    if ($favoritedTreatments.length > 0) {
      addTreatmentsToMenu($favoritedTreatments);
      return;
    }

    $favoriteTreatmentsList.html(`
      <div class="px-4 py-2 text-sm text-gray-500 italic">
        No favorite treatments
      </div>
    `);
    return;
  }

  addTreatmentsToMenu($favoriteTreatments);
}

function addTreatmentsToMenu($treatments) {
  const $favoriteTreatmentsList = $('#favoriteTreatmentsList');

  if ($treatments.length === 0) {
    $favoriteTreatmentsList.html(`
      <div class="px-4 py-2 text-sm text-gray-500 italic">
        No favorite treatments
      </div>
    `);
    return;
  }

  // Create horizontal scrollable container
  $favoriteTreatmentsList.html(`
    <div class="overflow-x-auto px-2 py-1">
      <div class="flex gap-1 min-w-max" style="width: max-content;">
        <!-- Treatment boxes will be added here -->
      </div>
    </div>
  `);

  const $treatmentContainer = $favoriteTreatmentsList.find('.flex');

  $treatments.each(function() {
    const $treatment = $(this).find('.treatment');
    const treatmentId = $treatment.data('treatment-id');
    const treatmentName = $treatment.text().trim();
    const treatmentFolder = $treatment.data('treatment-folder') || treatmentName.toLowerCase();
    const treatmentRegion = $treatment.data('region');
    const isFullTooth = $treatment.data('treatment-full');

    let treatmentPreviewHtml = '';

    if (treatmentRegion === 'Patient') {
      // Patient treatment - show only user icon, no tooth or treatment image
      treatmentPreviewHtml = `
        <div class="relative w-[46px] h-[46px] mx-auto mb-1 flex items-center justify-center">
          <span class="material-symbols-outlined text-gray-600" style="font-size: 32px;">
            person
          </span>
        </div>
      `;
    } else if (treatmentRegion === 'Tooth') {
      // Tooth treatment - use incisal surface for non-full-tooth treatments, regular for full-tooth
      const toothTop = `https://upodmedican.b-cdn.net/Adult%20Teeth%20Individual%20Final/UL1.png`;
      let toothTreatmentTop;

      if (isFullTooth) {
        toothTreatmentTop = `https://upodmedican.b-cdn.net/${treatmentFolder}/UL1.png`;
      } else {
        toothTreatmentTop = `https://upodmedican.b-cdn.net/${treatmentFolder}/UL1_incisal.png`;
      }

      treatmentPreviewHtml = `
        <div class="relative w-[46px] h-[46px] mx-auto mb-1">
          <img src="${toothTop}" 
               class="w-full h-full object-contain" 
               alt="Tooth">
          <img src="${toothTreatmentTop}" 
               class="absolute inset-0 w-full h-full object-contain treatment-id-${treatmentId} tooth-treatment-img" 
               alt="">
        </div>
      `;
    } else {
      // Default fallback - use regular tooth with treatment overlay
      const toothTop = `https://upodmedican.b-cdn.net/Adult%20Teeth%20Individual%20Final/UL1.png`;
      const toothTreatmentTop = `https://upodmedican.b-cdn.net/${treatmentFolder}/UL1_incisal.png`;

      treatmentPreviewHtml = `
        <div class="relative w-[46px] h-[46px] mx-auto mb-1">
          <img src="${toothTop}" 
               class="w-full h-full object-contain" 
               alt="Tooth">
          <img src="${toothTreatmentTop}" 
               class="absolute inset-0 w-full h-full object-contain treatment-id-${treatmentId} tooth-treatment-img" 
               alt="">
        </div>
      `;
    }

    // Create treatment box with preview
    const treatmentBox = `
      <div class="favorite-treatment-item relative flex-shrink-0 w-[58px] cursor-pointer group rounded-md hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-all duration-150 p-1" 
           data-treatment-id="${treatmentId}">
        <!-- Treatment Preview -->
        ${treatmentPreviewHtml}
        
        <!-- Treatment Name -->
        <div class="text-[9px] text-center text-gray-700 group-hover:text-blue-600 font-medium px-0.5 leading-tight">
          ${treatmentName.length > 12 ? treatmentName.substring(0, 10) + '...' : treatmentName}
        </div>
      </div>
    `;

    $treatmentContainer.append(treatmentBox);
  });
}
