// Patient Notes Mute Functionality
// Handles the muting of auto-expanded notes for a configurable duration

document.addEventListener('DOMContentLoaded', function() {
  initializeMuteNotesDropdown();
  checkAndClearNextSelectMute();
});

function initializeMuteNotesDropdown() {
  // Find the dropdown trigger button and menu
  const container = document.querySelector('.mute-notes-container');
  const trigger = document.querySelector('.mute-notes-trigger');
  const menu = document.querySelector('.mute-notes-menu');
  const muteOptions = document.querySelectorAll('.mute-option');
  
  if (!container || !trigger || !menu) return;
  
  // Toggle dropdown when clicking the trigger button
  trigger.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const isExpanded = trigger.getAttribute('aria-expanded') === 'true';
    
    // Toggle dropdown state
    trigger.setAttribute('aria-expanded', !isExpanded);
    
    if (!isExpanded) {
      showMuteMenu(menu, trigger);
      document.addEventListener('click', closeMenuOnClickOutside);
    } else {
      hideMenu(menu);
      document.removeEventListener('click', closeMenuOnClickOutside);
    }
  });
  
  // Close dropdown when clicking outside
  function closeMenuOnClickOutside(e) {
    if (!container.contains(e.target)) {
      hideMenu(menu);
      trigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeMenuOnClickOutside);
    }
  }
  
  // Close dropdown when pressing Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && trigger.getAttribute('aria-expanded') === 'true') {
      hideMenu(menu);
      trigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeMenuOnClickOutside);
    }
  });
  
  // Handle mute option selection
  muteOptions.forEach(option => {
    option.addEventListener('click', function() {
      const muteOption = this.getAttribute('data-mute-option');
      const patientId = getPatientId();
      
      if (!patientId) return;
      
      muteNotesForPatient(patientId, muteOption);
      hideMenu(menu);
      trigger.setAttribute('aria-expanded', 'false');
      
      // Show feedback to user
      showMuteFeedback(muteOption);
    });
  });
}

function showMuteMenu(menu, trigger) {
  // Simple show/hide since positioning is handled by the parent dropdown clone
  menu.classList.remove('hidden');
}

function hideMenu(menu) {
  menu.classList.add('hidden');
}

function getPatientId() {
  // Try to get patient ID from URL
  const urlMatch = window.location.pathname.match(/\/admin\/patients\/(\d+)/);
  if (urlMatch && urlMatch[1]) {
    return urlMatch[1];
  }
  
  // If not found in URL, try to get from a data attribute on the page
  const patientContainer = document.querySelector('[data-patient-id]');
  if (patientContainer) {
    return patientContainer.getAttribute('data-patient-id');
  }
  
  return null;
}

function muteNotesForPatient(patientId, muteOption) {
  const now = new Date();
  let expiryTime;
  
  switch(muteOption) {
    case 'next-select':
      // Special case - store without expiry time
      expiryTime = 'next-select';
      break;
    case '15min':
      expiryTime = new Date(now.getTime() + 15 * 60 * 1000).getTime();
      break;
    case '30min':
      expiryTime = new Date(now.getTime() + 30 * 60 * 1000).getTime();
      break;
    case '1hour':
      expiryTime = new Date(now.getTime() + 60 * 60 * 1000).getTime();
      break;
    case '4hours':
      expiryTime = new Date(now.getTime() + 4 * 60 * 60 * 1000).getTime();
      break;
    case 'tomorrow':
      // Set to 8:00 AM tomorrow
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(8, 0, 0, 0);
      expiryTime = tomorrow.getTime();
      break;
    default:
      // Default to 1 hour if something goes wrong
      expiryTime = new Date(now.getTime() + 60 * 60 * 1000).getTime();
  }
  
  // Store in localStorage
  localStorage.setItem(`muted_notes_${patientId}`, JSON.stringify({
    muted: true,
    expiryTime: expiryTime,
    muteOption: muteOption,
    timestamp: now.getTime()
  }));
}

function showMuteFeedback(muteOption) {
  // Get the message based on mute option
  let message;
  switch(muteOption) {
    case 'next-select':
      message = 'Notes muted until you next select this patient';
      break;
    case '15min':
      message = 'Notes muted for 15 minutes';
      break;
    case '30min':
      message = 'Notes muted for 30 minutes';
      break;
    case '1hour':
      message = 'Notes muted for 1 hour';
      break;
    case '4hours':
      message = 'Notes muted for 4 hours';
      break;
    case 'tomorrow':
      message = 'Notes muted until tomorrow';
      break;
    default:
      message = 'Notes muted';
  }
  
  // Configure toastr options
  toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: 'toast-top-right',
    timeOut: 3000
  };
  
  // Show the toastr notification
  toastr.success(message, 'Notes Muted');
}

// Function to check if notes are muted for the current patient
function areNotesMuted(patientId) {
  const muteData = localStorage.getItem(`muted_notes_${patientId}`);
  
  if (!muteData) return false;
  
  try {
    const data = JSON.parse(muteData);
    
    // If mute option is "next-select", it's always muted until explicitly cleared
    if (data.expiryTime === 'next-select') {
      return true;
    }
    
    // Check if the mute has expired
    const now = new Date().getTime();
    return data.muted && now < data.expiryTime;
  } catch (e) {
    return false;
  }
}

// Function to clear "next-select" mute when changing patients
function clearNextSelectMute(patientId) {
  const muteData = localStorage.getItem(`muted_notes_${patientId}`);

  if (!muteData) return;

  try {
    const data = JSON.parse(muteData);

    if (data.muteOption === 'next-select') {
      localStorage.removeItem(`muted_notes_${patientId}`);
    }
  } catch (e) {
    // Ignore errors
  }
}

// Function to check if current page is main patient show page (not sub-pages)
function isMainPatientShowPage() {
  const pathname = window.location.pathname;

  // Match pattern: /admin/patients/{id} (but not /admin/patients/{id}/something)
  const mainPatientPagePattern = /^\/admin\/patients\/\d+$/;

  return mainPatientPagePattern.test(pathname);
}

// Function to check and clear "next-select" mute if on main patient show page
function checkAndClearNextSelectMute() {
  // Only clear mute if we're on the main patient show page
  if (!isMainPatientShowPage()) {
    return;
  }

  const patientId = getPatientId();
  if (!patientId) return;

  clearNextSelectMute(patientId);
}

// Export functions for use in other files
window.patientNotesMute = {
  areNotesMuted,
  clearNextSelectMute,
  checkAndClearNextSelectMute,
  isMainPatientShowPage
};
