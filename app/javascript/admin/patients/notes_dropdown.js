// Patient Notes Dropdown
// Handles the dropdown functionality for the Notes button in the patient header
// Uses cloning pattern to escape container constraints (similar to account dropdowns)

// Global variables to store references for event listeners
let globalDropdownMenu = null;
let globalDropdownTrigger = null;

document.addEventListener('DOMContentLoaded', function() {
  initializeNotesDropdown();
});

// Throttle function to limit how often scroll events fire
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

// Function to reposition dropdown on scroll/resize (needs to be global)
function repositionOnScroll() {
  if (globalDropdownMenu && globalDropdownTrigger && !globalDropdownMenu.classList.contains('hidden')) {
    positionDropdownMenu(globalDropdownMenu, globalDropdownTrigger);
  }
}

// Throttled version of the reposition function
const throttledRepositionOnScroll = throttle(repositionOnScroll, 16); // ~60fps

function initializeNotesDropdown() {
  // Find the dropdown trigger button and menu
  const dropdown = document.querySelector('.notes-dropdown-container');
  const dropdownTrigger = document.querySelector('.notes-dropdown-trigger');
  const dropdownMenu = document.querySelector('.notes-dropdown-menu');

  if (!dropdown || !dropdownTrigger || !dropdownMenu) return;

  // Store global references
  globalDropdownMenu = dropdownMenu;
  globalDropdownTrigger = dropdownTrigger;

  // Check if notes should be expanded initially
  const initiallyExpanded = dropdownTrigger.getAttribute('aria-expanded') === 'true';
  const patientId = getPatientId();

  // If it should be expanded initially and not muted, show the dropdown
  if (initiallyExpanded && patientId && !isNotesMuted(patientId)) {
    // Add a delay to ensure this runs after any conflicting JavaScript on the charting page
    setTimeout(() => {
      // Double-check that the dropdown should still be expanded (in case something changed)
      if (dropdownTrigger.getAttribute('aria-expanded') === 'true') {
        // Position the dropdown relative to the button before showing
        positionDropdownMenu(dropdownMenu, dropdownTrigger);
        dropdownMenu.classList.remove('hidden');
        document.addEventListener('click', closeDropdownOnClickOutside);
        // Add scroll listener to reposition dropdown when scrolling
        window.addEventListener('scroll', throttledRepositionOnScroll);
        window.addEventListener('resize', throttledRepositionOnScroll);

        // Mark the dropdown as auto-opened to prevent immediate closure
        dropdownMenu.setAttribute('data-auto-opened', 'true');
        setTimeout(() => {
          dropdownMenu.removeAttribute('data-auto-opened');
        }, 500); // Remove the protection after 500ms
      }
    }, 200); // 200ms delay to ensure all other scripts have loaded
  } else if (initiallyExpanded) {
    // Notes are muted, so don't show them but keep aria-expanded consistent with the DOM
    dropdownTrigger.setAttribute('aria-expanded', 'false');
  }

  // Toggle dropdown when clicking the trigger button
  dropdownTrigger.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const isExpanded = dropdownTrigger.getAttribute('aria-expanded') === 'true';

    // Toggle dropdown state
    dropdownTrigger.setAttribute('aria-expanded', !isExpanded);

    if (!isExpanded) {
      // Position the dropdown relative to the button before showing
      positionDropdownMenu(dropdownMenu, dropdownTrigger);
      dropdownMenu.classList.remove('hidden');
      document.addEventListener('click', closeDropdownOnClickOutside);
      // Add scroll listener to reposition dropdown when scrolling
      window.addEventListener('scroll', throttledRepositionOnScroll);
      window.addEventListener('resize', throttledRepositionOnScroll);
    } else {
      // Remove scroll listeners when closing
      window.removeEventListener('scroll', throttledRepositionOnScroll);
      window.removeEventListener('resize', throttledRepositionOnScroll);
        dropdownMenu.classList.add('hidden');
        document.removeEventListener('click', closeDropdownOnClickOutside);
    }
  });

  // Close dropdown when clicking outside
  function closeDropdownOnClickOutside(e) {
    // Don't close if the dropdown was just auto-opened (protection period)
    if (dropdownMenu.getAttribute('data-auto-opened') === 'true') {
      return;
    }

    if (!dropdown.contains(e.target) && !dropdownMenu.contains(e.target)) {
      dropdownMenu.classList.add('hidden');
      dropdownTrigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeDropdownOnClickOutside);
      // Remove scroll listeners when closing
      window.removeEventListener('scroll', throttledRepositionOnScroll);
      window.removeEventListener('resize', throttledRepositionOnScroll);
    }
  }

  // Close dropdown when pressing Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && dropdownTrigger.getAttribute('aria-expanded') === 'true') {
      dropdownMenu.classList.add('hidden');
      dropdownTrigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeDropdownOnClickOutside);
      // Remove scroll listeners when closing
      window.removeEventListener('scroll', repositionOnScroll);
      window.removeEventListener('resize', repositionOnScroll);
    }
  });


}

// Function to position the dropdown menu relative to the trigger button
function positionDropdownMenu(menu, button) {
  // Clear any existing positioning styles
  menu.style.top = '';
  menu.style.bottom = '';
  menu.style.left = '';
  menu.style.right = '';
  menu.style.marginTop = '';
  menu.style.marginBottom = '';

  // Get the button's position relative to the document
  const buttonRect = button.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Calculate button's absolute position in the document
  const buttonTop = buttonRect.top + scrollTop;
  const buttonLeft = buttonRect.left + scrollLeft;
  const buttonBottom = buttonRect.bottom + scrollTop;
  const buttonRight = buttonRect.right + scrollLeft;

  // Get menu dimensions (temporarily show it to measure if hidden)
  const wasHidden = menu.classList.contains('hidden');
  if (wasHidden) {
    menu.style.visibility = 'hidden';
    menu.classList.remove('hidden');
  }

  const menuRect = menu.getBoundingClientRect();
  const menuWidth = menuRect.width;
  const menuHeight = menuRect.height;

  if (wasHidden) {
    menu.classList.add('hidden');
    menu.style.visibility = '';
  }

  // Calculate viewport dimensions
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Calculate space below and above the button in viewport
  const spaceBelow = viewportHeight - (buttonRect.bottom);
  const spaceAbove = buttonRect.top;

  // Position vertically - prefer below the button
  if (spaceBelow >= menuHeight + 10) {
    // Enough space below - position below the button
    menu.style.top = (buttonBottom + 5) + 'px';
  } else if (spaceAbove >= menuHeight + 10) {
    // Not enough space below but enough above - position above the button
    menu.style.top = (buttonTop - menuHeight - 5) + 'px';
  } else {
    // Not enough space above or below - position below anyway
    menu.style.top = (buttonBottom + 5) + 'px';
  }

  // Position horizontally - align right edge of menu with right edge of button
  const rightAlignedLeft = buttonRight - menuWidth;

  // Check if right-aligned position would go off-screen
  if (rightAlignedLeft >= scrollLeft && (rightAlignedLeft + menuWidth) <= (scrollLeft + viewportWidth)) {
    // Right-align with button
    menu.style.left = rightAlignedLeft + 'px';
  } else if (buttonLeft >= scrollLeft && (buttonLeft + menuWidth) <= (scrollLeft + viewportWidth)) {
    // Left-align with button
    menu.style.left = buttonLeft + 'px';
  } else {
    // Center in viewport if button is off-screen
    menu.style.left = (scrollLeft + (viewportWidth - menuWidth) / 2) + 'px';
  }
}



function getPatientId() {
  // Try to get patient ID from URL
  const urlMatch = window.location.pathname.match(/\/admin\/patients\/(\d+)/);
  if (urlMatch && urlMatch[1]) {
    return urlMatch[1];
  }
  
  // If not found in URL, try to get from a data attribute on the page
  const patientContainer = document.querySelector('[data-patient-id]');
  if (patientContainer) {
    return patientContainer.getAttribute('data-patient-id');
  }
  
  return null;
}

function isNotesMuted(patientId) {
  // Check if window.patientNotesMute exists (from mute_notes.js)
  if (window.patientNotesMute && window.patientNotesMute.areNotesMuted) {
    return window.patientNotesMute.areNotesMuted(patientId);
  }
  
  // Fallback implementation if mute_notes.js isn't loaded yet
  const muteData = localStorage.getItem(`muted_notes_${patientId}`);
  
  if (!muteData) return false;
  
  try {
    const data = JSON.parse(muteData);
    
    // If mute option is "next-select", it's always muted until explicitly cleared
    if (data.expiryTime === 'next-select') {
      return true;
    }
    
    // Check if the mute has expired
    const now = new Date().getTime();
    return data.muted && now < data.expiryTime;
  } catch (e) {
    return false;
  }
}
