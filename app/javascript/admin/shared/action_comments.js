/**
 * Action Comments
 * 
 * This file handles the functionality of the action comments section
 * that shows comments under each action card
 */

document.addEventListener('DOMContentLoaded', function() {
  initActionComments();
});

/**
 * Initialize the action comments functionality
 */
function initActionComments() {
  // Find all comment buttons in the actions sidebar (both old and new systems)
  const newCommentIcons = document.querySelectorAll('.lucide-message-square');
  const oldCommentButtons = document.querySelectorAll('.comments-toggle');

  // Handle new system (lucide icons)
  newCommentIcons.forEach(icon => {
    const button = icon.closest('button');
    if (button) {
      button.classList.add('action-comment-btn');
      setupCommentButtonListener(button);
    }
  });

  // Handle old system (comments-toggle buttons)
  oldCommentButtons.forEach(button => {
    button.classList.add('action-comment-btn');
    setupCommentButtonListener(button);
  });

  // Handle Font Awesome comment buttons - find buttons containing fa-message-lines icons
  const allButtons = document.querySelectorAll('button');
  allButtons.forEach(button => {
    const faIcon = button.querySelector('.fa-message-lines');
    if (faIcon) {
      button.classList.add('action-comment-btn');
      setupCommentButtonListener(button);
    }
  });
}

/**
 * Set up click listener for a comment button
 * @param {HTMLElement} button - The comment button element
 */
function setupCommentButtonListener(button) {
      
      // Add click event listener
      button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        // Mark this button as being handled by comments to prevent timeline handlers
        this.setAttribute('data-comments-handled', 'true');

        // Get the action card (try multiple possible containers)
        // For table layouts, prioritize finding the table row first
        let actionCard = this.closest('tr'); // For table rows
        if (!actionCard) {
          actionCard = this.closest('.action-card');
        }
        if (!actionCard) {
          actionCard = this.closest('.action-item');
        }
        // Only use [data-action-id] as last resort to avoid finding the button itself
        if (!actionCard) {
          const dataActionElement = this.closest('[data-action-id]');
          // Make sure it's not the button itself
          if (dataActionElement && dataActionElement !== this) {
            actionCard = dataActionElement;
          }
        }

        if (!actionCard) {
          return;
        }

        // Get the action ID (try multiple sources)
        let actionId = this.getAttribute('data-action-id');
        if (!actionId) {
          actionId = actionCard.getAttribute('data-action-id');
        }
        if (!actionId) {
          const actionContainer = actionCard.querySelector('[data-action-id]');
          actionId = actionContainer ? actionContainer.getAttribute('data-action-id') : null;
        }
        if (!actionId) {
          // Try to extract from URL or other attributes
          const actionLink = actionCard.querySelector('a[href*="/actions/"]');
          if (actionLink) {
            const match = actionLink.href.match(/\/actions\/(\d+)/);
            actionId = match ? match[1] : null;
          }
        }

        if (!actionId) {
          return;
        }
        
        // Check if comments section already exists (handle both div and table row cases)


        let commentsSection = actionCard.querySelector('.action-comments-section');
        if (!commentsSection && actionCard.tagName === 'TR') {
          // For table rows, look for the comments row after this row
          const nextRow = actionCard.nextElementSibling;
          if (nextRow && nextRow.classList.contains('action-comments-row')) {
            commentsSection = nextRow;
          }
        }

        // Also check for comments section after the action card container (for sidebar layout)
        if (!commentsSection) {
          const actionCardContainer = actionCard.querySelector('.action-card-container');
          if (actionCardContainer && actionCardContainer.nextElementSibling) {
            const nextElement = actionCardContainer.nextElementSibling;
            if (nextElement.classList.contains('action-comments-section') && nextElement.dataset.actionId === actionId) {
              commentsSection = nextElement;
            }
          }

          // Also check directly after the action card
          if (!commentsSection && actionCard.nextElementSibling) {
            const nextElement = actionCard.nextElementSibling;
            if (nextElement.classList.contains('action-comments-section') && nextElement.dataset.actionId === actionId) {
              commentsSection = nextElement;
            }
          }
        }



        // If comments section doesn't exist, create it
        if (!commentsSection) {
          commentsSection = createCommentsSection(actionCard, actionId);
          // Fetch and load the comments data
          fetchCommentsData(actionId, commentsSection);

          // Close other sections before showing this one
          closeOtherSections(commentsSection);

          // Show the newly created comments section
          if (commentsSection.classList.contains('action-comments-row')) {
            commentsSection.style.display = 'table-row';

            // Also remove hidden class from the inner comments section div
            const innerCommentsSection = commentsSection.querySelector('.action-comments-section');
            if (innerCommentsSection) {
              innerCommentsSection.classList.remove('hidden');
            }
          } else {
            commentsSection.classList.remove('hidden');
          }
          button.classList.add('active');
        } else {
          // Comments section exists, so toggle it
          // Check if button is active to determine visibility state
          const isCurrentlyVisible = button.classList.contains('active');

          if (isCurrentlyVisible) {
            // Hide the comments section
            if (commentsSection.classList.contains('action-comments-row')) {
              commentsSection.style.display = 'none';
            } else {
              commentsSection.classList.add('hidden');
            }
            button.classList.remove('active');
          } else {
            // Close other sections before showing this one
            closeOtherSections(commentsSection);

            // Show the existing comments section
            if (commentsSection.classList.contains('action-comments-row')) {
              commentsSection.style.display = 'table-row';

              // Also remove hidden class from the inner comments section div
              const innerCommentsSection = commentsSection.querySelector('.action-comments-section');
              if (innerCommentsSection) {
                innerCommentsSection.classList.remove('hidden');
              }
            } else {
              commentsSection.classList.remove('hidden');
            }
            button.classList.add('active');
          }
        }

      });
}

/**
 * Close other open comments sections and timeline accordions
 * @param {HTMLElement} currentCommentsSection - The current comments section to keep open
 */
function closeOtherSections(currentCommentsSection) {
  // Close any other open comments sections
  document.querySelectorAll('.action-comments-section:not(.hidden)').forEach(section => {
    if (section !== currentCommentsSection) {
      section.classList.add('hidden');

      // Reset the button appearance
      const btn = section.closest('.action-card')?.querySelector('.action-comment-btn');
      if (btn) {
        btn.classList.remove('active');
      }
    }
  });

  // Also close any other open table row comments
  document.querySelectorAll('.action-comments-row').forEach(row => {
    if (row !== currentCommentsSection) {
      row.style.display = 'none';

      // Reset the button appearance
      const actionRow = row.previousElementSibling;
      if (actionRow) {
        const btn = actionRow.querySelector('.action-comment-btn');
        if (btn) {
          btn.classList.remove('active');
        }
      }
    }
  });

  // Close any open timeline accordions
  document.querySelectorAll('.action-timeline-accordion:not(.hidden)').forEach(accordion => {
    accordion.classList.add('hidden');

    // Reset the clock button appearance
    const btn = accordion.closest('.action-card')?.querySelector('.action-clock-btn');
    if (btn) {
      btn.classList.remove('active');
    }
  });
}

/**
 * Create the comments section HTML
 * @param {HTMLElement} actionCard - The action card element
 * @param {string} actionId - The ID of the action
 * @returns {HTMLElement} - The comments section element
 */
function createCommentsSection(actionCard, actionId) {
  // Create the comments section element
  let commentsSection = document.createElement('div');
  commentsSection.className = 'action-comments-section bg-gray-50 p-4';
  commentsSection.setAttribute('data-action-id', actionId);
  
  // Create the comments section HTML
  commentsSection.innerHTML = `
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-sm font-medium text-gray-900 flex items-center">
        Comments <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full comments-count">0</span>
      </h3>
      <div class="flex items-center gap-2">
        <div class="relative">
          <button class="sort-comments-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-md h-8 px-3 text-gray-700">
            <span class="sort-label">Newest</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4">
              <path d="m6 9 6 6 6-6"></path>
            </svg>
          </button>
          <div class="sort-dropdown absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10 hidden">
            <ul class="py-1">
              <li>
                <button class="sort-option w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-sort="newest">Newest</button>
              </li>
              <li>
                <button class="sort-option w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-sort="oldest">Oldest</button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="comments-container"></div>
    <div class="mt-4">
      <div class="flex items-center gap-2">
        <div class="relative flex-1">
          <input type="text" class="comment-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Add your thoughts...">
        </div>
        <button class="send-comment-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-blue-600 hover:bg-blue-700 text-white rounded-md h-10 w-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send">
            <path d="m22 2-7 20-4-9-9-4Z"></path>
            <path d="M22 2 11 13"></path>
          </svg>
        </button>
      </div>
    </div>
  `;
  
  // Insert the comments section appropriately based on the layout
  const actionCardContainer = actionCard.querySelector('.action-card-container');

  if (actionCardContainer) {
    actionCardContainer.parentNode.insertBefore(commentsSection, actionCardContainer.nextSibling);
  } else if (actionCard.tagName === 'TR') {
    // For table layouts, create a new table row
    const commentsRow = document.createElement('tr');
    commentsRow.className = 'action-comments-row bg-gray-50';
    commentsRow.setAttribute('data-action-id', actionId);
    commentsRow.style.display = 'none'; // Initially hidden

    // Get the number of columns in the table
    const columnCount = actionCard.children.length;

    // Create a single cell that spans all columns
    const commentsCell = document.createElement('td');
    commentsCell.setAttribute('colspan', columnCount);
    commentsCell.className = 'p-4 rounded-xl bg-gray-50';

    // Move the comments section into the cell
    commentsCell.appendChild(commentsSection);
    commentsRow.appendChild(commentsCell);

    // Insert the row after the action row
    actionCard.parentNode.insertBefore(commentsRow, actionCard.nextSibling);

    // Update the reference to point to the row instead of the section
    commentsSection = commentsRow;
  } else {
    actionCard.appendChild(commentsSection);
  }

  // Add event listeners to the comments section
  addCommentsSectionEventListeners(commentsSection, actionId);

  return commentsSection;
}

/**
 * Add event listeners to the comments section
 * @param {HTMLElement} commentsSection - The comments section element
 * @param {string} actionId - The ID of the action
 */
function addCommentsSectionEventListeners(commentsSection, actionId) {
  // Add event listener to the sort dropdown button
  const sortButton = commentsSection.querySelector('.sort-comments-btn');
  const sortDropdown = commentsSection.querySelector('.sort-dropdown');
  const sortOptions = commentsSection.querySelectorAll('.sort-option');
  const sortLabel = commentsSection.querySelector('.sort-label');
  
  if (sortButton && sortDropdown) {
    // Toggle sort dropdown
    sortButton.addEventListener('click', function() {
      sortDropdown.classList.toggle('hidden');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!sortButton.contains(e.target) && !sortDropdown.contains(e.target)) {
        sortDropdown.classList.add('hidden');
      }
    });
    
    // Handle sort option selection
    sortOptions.forEach(option => {
      option.addEventListener('click', function() {
        const sortOrder = this.getAttribute('data-sort');
        sortLabel.textContent = sortOrder.charAt(0).toUpperCase() + sortOrder.slice(1);
        sortDropdown.classList.add('hidden');
        sortComments(commentsSection, sortOrder);
      });
    });
  }
  
  // Add event listener to the send button
  const sendButton = commentsSection.querySelector('.send-comment-btn');
  const commentInput = commentsSection.querySelector('.comment-input');
  
  if (sendButton && commentInput) {
    // Add click event listener to the send button
    sendButton.addEventListener('click', function() {
      const commentText = commentInput.value.trim();
      if (commentText) {
        sendComment(actionId, commentText, commentsSection);
        commentInput.value = '';
      }
    });
    
    // Add keypress event listener to the comment input
    commentInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        const commentText = this.value.trim();
        if (commentText) {
          sendComment(actionId, commentText, commentsSection);
          this.value = '';
        }
      }
    });
  }
  
  // No global reply form handlers needed as we're using inline reply forms
}

/**
 * Fetch comments data from the server
 * @param {string} actionId - The ID of the action
 * @param {HTMLElement} commentsSection - The comments section element
 */
function fetchCommentsData(actionId, commentsSection) {
  // Show loading state
  const commentsContainer = commentsSection.querySelector('.comments-container');

  if (commentsContainer) {
    commentsContainer.innerHTML = `
      <div class="flex justify-center items-center py-4">
        <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="ml-2 text-sm text-gray-600">Loading comments...</span>
      </div>
    `;
  }

  // Create a new XMLHttpRequest
  const xhr = new XMLHttpRequest();

  // Configure the request
  const url = `/admin/action_comments?action_id=${actionId}`;

  xhr.open('GET', url, true);
  xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  xhr.setRequestHeader('Accept', 'application/json');

  // Set up the callback for when the request completes
  xhr.onload = function() {
    if (xhr.status === 200) {
      try {
        const response = JSON.parse(xhr.responseText);
        displayComments(response.comments, commentsSection);
      } catch (e) {
        showCommentsError(commentsSection, 'Error parsing comments data');
      }
    } else {
      showCommentsError(commentsSection, 'Error loading comments data');
    }
  };
  
  // Set up error handling
  xhr.onerror = function() {
    console.error('Network error when trying to fetch comments data');
    showCommentsError(commentsSection, 'Network error when loading comments');
  };
  
  // Send the request
  xhr.send();
}

/**
 * Display comments in the comments section
 * @param {Array} comments - The comments data
 * @param {HTMLElement} commentsSection - The comments section element
 */
function displayComments(comments, commentsSection) {
  const commentsContainer = commentsSection.querySelector('.comments-container');
  const commentsCount = commentsSection.querySelector('.comments-count');

  if (commentsContainer) {
    // Clear the comments container
    commentsContainer.innerHTML = '';

    // Update the comments count
    if (commentsCount) {
      commentsCount.textContent = comments.length;
    }

    // If there are no comments, show a message
    if (comments.length === 0) {
      commentsContainer.innerHTML = '<p class="text-sm text-gray-500 text-center py-4">No comments yet</p>';
      return;
    }
    
    // Add each comment to the container
    comments.forEach(comment => {
      const commentHTML = createCommentHTML(comment);
      commentsContainer.insertAdjacentHTML('beforeend', commentHTML);
      
      // Add event listener to the reply button
      const replyButton = commentsContainer.querySelector(`.reply-btn[data-comment-id="${comment.id}"]`);
      if (replyButton) {
        replyButton.addEventListener('click', function() {
          // Find the comment card
          const commentCard = this.closest('.comment-card');
          if (!commentCard) return;

          // First, close any other open reply forms in the comments section
          const allReplyForms = commentsSection.querySelectorAll('.reply-form-container');
          allReplyForms.forEach(form => {
            if (!commentCard.contains(form)) {
              form.remove();
            }
          });

          // Check if there's already a reply form in this comment
          let replyFormContainer = commentCard.querySelector('.reply-form-container');

          // If there's no reply form container, create one
          if (!replyFormContainer) {
            replyFormContainer = document.createElement('div');
            replyFormContainer.className = 'reply-form-container mt-3 pl-4 border-l-2 border-gray-100';
            
            // Create the reply form HTML
            replyFormContainer.innerHTML = `
              <div class="flex items-center gap-2">
                <div class="relative flex-1">
                  <input type="text" class="reply-input w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Add your reply...">
                  <input type="hidden" class="reply-parent-id" value="${comment.id}">
                </div>
                <button class="send-reply-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-blue-600 hover:bg-blue-700 text-white rounded-md h-10 w-10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send">
                    <path d="m22 2-7 20-4-9-9-4Z"></path>
                    <path d="M22 2 11 13"></path>
                  </svg>
                </button>
                <button class="cancel-reply-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md h-10 w-10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                  </svg>
                </button>
              </div>
            `;
            
            // Add the reply form container to the comment card
            const repliesContainer = commentCard.querySelector('.replies');
            if (repliesContainer) {
              repliesContainer.appendChild(replyFormContainer);
            } else {
              // If there's no replies container, create one and add the reply form
              const newRepliesContainer = document.createElement('div');
              newRepliesContainer.className = 'replies pl-4 pr-3 pb-3';
              newRepliesContainer.appendChild(replyFormContainer);
              commentCard.appendChild(newRepliesContainer);
            }
            
            // Add event listeners to the reply form buttons
            const sendReplyBtn = replyFormContainer.querySelector('.send-reply-btn');
            const cancelReplyBtn = replyFormContainer.querySelector('.cancel-reply-btn');
            const replyInput = replyFormContainer.querySelector('.reply-input');
            const parentIdInput = replyFormContainer.querySelector('.reply-parent-id');
            
            if (sendReplyBtn && cancelReplyBtn && replyInput && parentIdInput) {
              // Send reply button
              sendReplyBtn.addEventListener('click', function() {
                const replyText = replyInput.value.trim();
                const parentId = parentIdInput.value;
                
                if (replyText && parentId) {
                  sendReply(commentsSection.getAttribute('data-action-id'), replyText, parentId, commentsSection);
                  replyFormContainer.remove();
                }
              });
              
              // Cancel reply button
              cancelReplyBtn.addEventListener('click', function() {
                replyFormContainer.remove();
              });
              
              // Send on Enter
              replyInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  const replyText = this.value.trim();
                  const parentId = parentIdInput.value;
                  
                  if (replyText && parentId) {
                    sendReply(commentsSection.getAttribute('data-action-id'), replyText, parentId, commentsSection);
                    replyFormContainer.remove();
                  }
                }
              });
              
              // Focus the input
              replyInput.focus();
            }
          } else {
            // If there's already a reply form, remove it (toggle behavior)
            replyFormContainer.remove();
          }
        });
      }
    });
  }
}

/**
 * Create HTML for a comment
 * @param {Object} comment - The comment object
 * @returns {string} - The HTML for the comment
 */
function createCommentHTML(comment) {
  // Convert the created_at string to a Date object if it's a string
  const createdAt = typeof comment.created_at === 'string' ? new Date(comment.created_at) : comment.created_at;
  const relativeTime = getRelativeTime(createdAt);

  // Get initials from user object or generate from name
  const initials = comment.user.initials || comment.user.name.split(' ').map(n => n[0]).join('');

  return `
    <div class="comment-card mb-3 bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden" data-comment-id="${comment.id}" data-created-at="${comment.created_at}">
      <div class="p-3">
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0">
            ${comment.user.avatar ? `<img src="${comment.user.avatar}" alt="${comment.user.name}" class="h-8 w-8 rounded-full">` : `
              <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-xs font-medium">
                ${initials}
              </div>
            `}
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex justify-between items-start">
              <p class="text-sm font-medium text-gray-900">${comment.user.name}</p>
              <p class="text-xs text-gray-500">${relativeTime}</p>
            </div>
            <p class="text-sm text-gray-700 mt-1">${comment.text}</p>
            <div class="mt-2 flex items-center gap-2">
              <button class="reply-btn text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1" data-comment-id="${comment.id}">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-corner-down-right">
                  <polyline points="15 10 20 15 15 20"></polyline>
                  <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                </svg>
                Reply
              </button>
            </div>
          </div>
        </div>
      </div>
      ${comment.replies && comment.replies.length > 0 ? `
        <div class="replies pl-4 pr-3 pb-3">
          ${comment.replies.map(reply => {
            // Convert the reply created_at string to a Date object if it's a string
            const replyCreatedAt = typeof reply.created_at === 'string' ? new Date(reply.created_at) : reply.created_at;
            const replyRelativeTime = getRelativeTime(replyCreatedAt);

            // Get reply initials
            const replyInitials = reply.user.initials || reply.user.name.split(' ').map(n => n[0]).join('');

            return `
              <div class="reply mt-2 pl-4 border-l-2 border-gray-100">
                <div class="flex items-start gap-2">
                  <div class="flex-shrink-0">
                    ${reply.user.avatar ? `<img src="${reply.user.avatar}" alt="${reply.user.name}" class="h-6 w-6 rounded-full">` : `
                      <div class="h-6 w-6 rounded-full bg-blue-50 flex items-center justify-center text-blue-600 text-xs font-medium">
                        ${replyInitials}
                      </div>
                    `}
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-start">
                      <p class="text-xs font-medium text-gray-900">${reply.user.name}</p>
                      <p class="text-xs text-gray-500">${replyRelativeTime}</p>
                    </div>
                    <p class="text-xs text-gray-700 mt-0.5">${reply.text}</p>
                  </div>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      ` : ''}
    </div>
  `;
}

/**
 * Show error message in the comments section
 * @param {HTMLElement} commentsSection - The comments section element
 * @param {string} message - The error message
 */
function showCommentsError(commentsSection, message) {
  const commentsContainer = commentsSection.querySelector('.comments-container');
  if (commentsContainer) {
    commentsContainer.innerHTML = `
      <div class="py-4 text-center">
        <p class="text-sm text-red-500">${message}. Please try again.</p>
      </div>
    `;
  }
}

/**
 * Send a comment to the server
 * @param {string} actionId - The ID of the action
 * @param {string} commentText - The comment text
 * @param {HTMLElement} commentsSection - The comments section element
 */
function sendComment(actionId, commentText, commentsSection) {
  // Show loading state in the input area
  const sendButton = commentsSection.querySelector('.send-comment-btn');
  const commentInput = commentsSection.querySelector('.comment-input');
  
  if (sendButton) {
    sendButton.disabled = true;
    sendButton.classList.add('opacity-50');
  }
  
  // Create a new XMLHttpRequest
  const xhr = new XMLHttpRequest();
  
  // Configure the request
  xhr.open('POST', '/admin/action_comments', true);
  xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.setRequestHeader('Accept', 'application/json');
  
  // Get CSRF token
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (csrfToken) {
    xhr.setRequestHeader('X-CSRF-Token', csrfToken);
  }
  
  // Set up the callback for when the request completes
  xhr.onload = function() {
    // Re-enable the send button
    if (sendButton) {
      sendButton.disabled = false;
      sendButton.classList.remove('opacity-50');
    }
    
    if (xhr.status === 200 || xhr.status === 201) {
      try {
        const response = JSON.parse(xhr.responseText);
        
        if (response.success) {
          // Get the comments container and count
          const commentsContainer = commentsSection.querySelector('.comments-container');
          const commentsCount = commentsSection.querySelector('.comments-count');
          
          if (commentsContainer) {
            // If there's a "no comments" message, remove it
            const noCommentsMessage = commentsContainer.querySelector('p.text-gray-500');
            if (noCommentsMessage) {
              commentsContainer.innerHTML = '';
            }
            
            // Add the new comment at the top
            const commentHTML = createCommentHTML(response.comment);
            commentsContainer.insertAdjacentHTML('afterbegin', commentHTML);
            
            // Update comments count
            if (commentsCount) {
              const currentCount = parseInt(commentsCount.textContent, 10);
              commentsCount.textContent = currentCount + 1;
            }
            
            // Add event listener to the reply button
            const replyButton = commentsContainer.querySelector(`.reply-btn[data-comment-id="${response.comment.id}"]`);
            if (replyButton) {
              replyButton.addEventListener('click', function() {
                // In a real implementation, you would show a reply form
                // Reply functionality would be implemented here
              });
            }
          }
        } else {
          console.error('Error saving comment:', response.errors);
          alert('Error saving comment: ' + response.errors.join(', '));
        }
      } catch (e) {
        console.error('Error parsing comment response:', e);
        alert('Error saving comment. Please try again.');
      }
    } else {
      console.error('Error saving comment:', xhr.status, xhr.statusText);
      alert('Error saving comment. Please try again.');
    }
  };
  
  // Set up error handling
  xhr.onerror = function() {
    console.error('Network error when trying to save comment');
    alert('Network error when saving comment. Please check your connection and try again.');
    
    // Re-enable the send button
    if (sendButton) {
      sendButton.disabled = false;
      sendButton.classList.remove('opacity-50');
    }
  };
  
  // Prepare the data
  const data = {
    action_comment: {
      action_id: actionId,
      comment: commentText
    }
  };
  
  // Send the request
  xhr.send(JSON.stringify(data));
}

/**
 * Send a reply to a comment
 * @param {string} actionId - The ID of the action
 * @param {string} replyText - The reply text
 * @param {string} parentId - The ID of the parent comment
 * @param {HTMLElement} commentsSection - The comments section element
 */
function sendReply(actionId, replyText, parentId, commentsSection) {
  // Show loading state
  const sendReplyBtn = commentsSection.querySelector('.send-reply-btn');
  if (sendReplyBtn) {
    sendReplyBtn.disabled = true;
    sendReplyBtn.classList.add('opacity-50');
  }
  
  // Create a new XMLHttpRequest
  const xhr = new XMLHttpRequest();
  
  // Configure the request
  xhr.open('POST', '/admin/action_comments', true);
  xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.setRequestHeader('Accept', 'application/json');
  
  // Get CSRF token
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (csrfToken) {
    xhr.setRequestHeader('X-CSRF-Token', csrfToken);
  }
  
  // Set up the callback for when the request completes
  xhr.onload = function() {
    // Re-enable the send button
    if (sendReplyBtn) {
      sendReplyBtn.disabled = false;
      sendReplyBtn.classList.remove('opacity-50');
    }
    
    if (xhr.status === 200 || xhr.status === 201) {
      try {
        const response = JSON.parse(xhr.responseText);
        
        if (response.success) {
          // Refresh the comments to show the new reply
          fetchCommentsData(actionId, commentsSection);
        } else {
          console.error('Error saving reply:', response.errors);
          alert('Error saving reply: ' + response.errors.join(', '));
        }
      } catch (e) {
        console.error('Error parsing reply response:', e);
        alert('Error saving reply. Please try again.');
      }
    } else {
      console.error('Error saving reply:', xhr.status, xhr.statusText);
      alert('Error saving reply. Please try again.');
    }
  };
  
  // Set up error handling
  xhr.onerror = function() {
    console.error('Network error when trying to save reply');
    alert('Network error when saving reply. Please check your connection and try again.');
    
    // Re-enable the send button
    if (sendReplyBtn) {
      sendReplyBtn.disabled = false;
      sendReplyBtn.classList.remove('opacity-50');
    }
  };
  
  // Prepare the data
  const data = {
    action_comment: {
      action_id: actionId,
      parent_id: parentId,
      comment: replyText
    }
  };
  
  // Send the request
  xhr.send(JSON.stringify(data));
}

/**
 * Get relative time from date
 * @param {Date} date - The date
 * @returns {string} - The relative time
 */
function getRelativeTime(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (diffDays === 0) {
    if (diffHours === 0) {
      return 'just now';
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays === 1) {
    return 'yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months !== 1 ? 's' : ''} ago`;
  }
}

/**
 * Sort comments in the comments section
 * @param {HTMLElement} commentsSection - The comments section element
 * @param {string} sortOrder - The sort order ('newest' or 'oldest')
 */
function sortComments(commentsSection, sortOrder) {
  const commentsContainer = commentsSection.querySelector('.comments-container');
  if (!commentsContainer) return;

  // Get all comment cards
  const commentCards = Array.from(commentsContainer.querySelectorAll('.comment-card'));

  // Sort the comment cards based on the sort order
  commentCards.sort((a, b) => {
    const aTime = new Date(a.dataset.createdAt || 0);
    const bTime = new Date(b.dataset.createdAt || 0);

    if (sortOrder === 'oldest') {
      return aTime - bTime;
    } else {
      return bTime - aTime; // newest first (default)
    }
  });

  // Clear the container and re-append sorted comments
  commentsContainer.innerHTML = '';
  commentCards.forEach(card => {
    commentsContainer.appendChild(card);
  });
}
