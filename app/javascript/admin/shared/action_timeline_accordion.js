/**
 * Action Timeline Accordion
 * 
 * This file handles the functionality of the action timeline accordion
 * that shows timeline entries under each action card
 */

document.addEventListener('DOMContentLoaded', function() {
  initActionTimelineAccordion();
});

/**
 * Initialize the action timeline accordion functionality
 */
function initActionTimelineAccordion() {
  // Find all clock buttons in the actions sidebar
  const clockButtons = document.querySelectorAll('.flex.items-center.justify-between.gap-2.p-2.bg-gray-50 button:first-child');
  
  // Add action-clock-btn class to all clock buttons (but skip comment buttons)
  clockButtons.forEach(button => {
    // Skip buttons that are already comment buttons
    if (button.classList.contains('action-comment-btn')) {
      return;
    }

    button.classList.add('action-clock-btn');
    
    // Add click event listener
    button.addEventListener('click', function(e) {
      // Skip if this button is being handled by comments
      if (this.hasAttribute('data-comments-handled')) {
        return;
      }

      e.preventDefault();
      e.stopPropagation();
      
      // Get the action card
      const actionCard = this.closest('.action-card');
      if (!actionCard) return;
      
      // Get the action ID
      const actionContainer = actionCard.querySelector('[data-action-id]');
      const actionId = actionContainer ? actionContainer.getAttribute('data-action-id') : null;
      if (!actionId) return;
      
      // Check if timeline accordion already exists
      let timelineAccordion = actionCard.querySelector('.action-timeline-accordion');
      
      // If accordion doesn't exist, create it
      if (!timelineAccordion) {
        timelineAccordion = createTimelineAccordion(actionCard, actionId);
        // Fetch and load the timeline data
        fetchTimelineData(actionId, timelineAccordion);
      }
      
      // Toggle the accordion
      if (timelineAccordion.classList.contains('hidden')) {
        // Close any open accordions first
        document.querySelectorAll('.action-timeline-accordion:not(.hidden)').forEach(accordion => {
          accordion.classList.add('hidden');
          
          // Reset the button appearance
          const btn = accordion.closest('.action-card').querySelector('.action-clock-btn');
          if (btn) {
            btn.classList.remove('active');
          }
        });
        
        // Close any open comments sections
        document.querySelectorAll('.action-comments-section:not(.hidden)').forEach(section => {
          section.classList.add('hidden');
          
          // Reset the comment button appearance
          const btn = section.closest('.action-card').querySelector('.action-comment-btn');
          if (btn) {
            btn.classList.remove('active');
          }
        });
        
        // Open this accordion
        timelineAccordion.classList.remove('hidden');
        button.classList.add('active');
      } else {
        // Close this accordion
        timelineAccordion.classList.add('hidden');
        button.classList.remove('active');
      }
    });
  });
}

/**
 * Create a timeline accordion for an action card
 * @param {HTMLElement} actionCard - The action card element
 * @param {string} actionId - The ID of the action
 * @returns {HTMLElement} - The created timeline accordion element
 */
function createTimelineAccordion(actionCard, actionId) {
  // Create the timeline accordion element
  const timelineAccordion = document.createElement('div');
  timelineAccordion.className = 'action-timeline-accordion hidden w-full overflow-hidden transition-all duration-300 ease-in-out';
  timelineAccordion.setAttribute('data-action-id', actionId);
  
  // Create the timeline container with loading state
  timelineAccordion.innerHTML = `
    <div class="p-6 bg-gradient-to-b from-gray-50 to-white">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-xs font-medium text-gray-700 uppercase tracking-wider flex items-center">
          <i class="fas fa-clock h-3.5 w-3.5 mr-2 text-gray-500"></i>
          <span>Action Timeline</span>
        </h4>
      </div>
      <div class="timeline-content space-y-3">
        <div class="flex justify-center items-center py-4">
          <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="ml-2 text-sm text-gray-600">Loading timeline...</span>
        </div>
      </div>
    </div>
  `;
  
  // Insert the timeline accordion after the action card container
  const actionCardContainer = actionCard.querySelector('.action-card-container');
  if (actionCardContainer) {
    actionCardContainer.parentNode.insertBefore(timelineAccordion, actionCardContainer.nextSibling);
  } else {
    actionCard.appendChild(timelineAccordion);
  }
  
  return timelineAccordion;
}

/**
 * Fetch timeline data from the server
 * @param {string} actionId - The ID of the action
 * @param {HTMLElement} timelineAccordion - The timeline accordion element
 */
function fetchTimelineData(actionId, timelineAccordion) {
  // Create a new XMLHttpRequest
  const xhr = new XMLHttpRequest();
  
  // Configure the request
  xhr.open('GET', `/admin/actions/${actionId}/timeline`, true);
  xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  
  // Set up the callback for when the request completes
  xhr.onload = function() {
    if (xhr.status === 200) {
      // Get the timeline content container
      const timelineContent = timelineAccordion.querySelector('.timeline-content');
      
      // Replace the content with the response
      if (timelineContent) {
        // Parse the HTML response
        const parser = new DOMParser();
        const doc = parser.parseFromString(xhr.responseText, 'text/html');
        
        // Clear the loading indicator
        timelineContent.innerHTML = '';
        
        // Process each timeline event and convert to our design
        const timelineEvents = doc.querySelectorAll('.timeline-event');
        
        if (timelineEvents.length === 0) {
          timelineContent.innerHTML = `
            <div class="py-4 text-center">
              <p class="text-sm text-gray-500">No timeline events found.</p>
            </div>
          `;
          return;
        }
        
        timelineEvents.forEach((event) => {
          // Extract data from the event
          const title = event.querySelector('h6')?.textContent || 'Event';
          const creatorInfo = event.querySelector('p')?.textContent || '';
          const comment = event.querySelector('small')?.textContent || null;
          
          // Parse creator info
          let creatorName = 'Unknown User';
          let creatorInitials = 'UN';
          let timestamp = '';
          
          if (creatorInfo) {
            const creatorMatch = creatorInfo.match(/(.+)\s+\|\s+(.+)/);
            if (creatorMatch) {
              creatorName = creatorMatch[1].trim();
              timestamp = creatorMatch[2].trim();
              creatorInitials = creatorName.split(' ').map(n => n[0]).join('');
            }
          }
          
          // Determine the icon class and action text based on the event class
          const eventClass = event.className;
          let iconBgClass = 'bg-blue-50';
          let iconTextClass = 'text-blue-500';
          let iconClass = 'fa-plus';
          let actionText = 'performed an action';
          
          if (eventClass.includes('created')) {
            iconBgClass = 'bg-blue-50';
            iconTextClass = 'text-blue-500';
            iconClass = 'fa-plus';
            actionText = 'created this action';
          } else if (eventClass.includes('comment')) {
            iconBgClass = 'bg-green-50';
            iconTextClass = 'text-green-500';
            iconClass = 'fa-comment';
            actionText = 'added a comment';
          } else if (eventClass.includes('assigned')) {
            iconBgClass = 'bg-purple-50';
            iconTextClass = 'text-purple-500';
            iconClass = 'fa-user-plus';
            actionText = 'assigned this action';
          } else if (eventClass.includes('completed')) {
            iconBgClass = 'bg-green-50';
            iconTextClass = 'text-green-500';
            iconClass = 'fa-check';
            actionText = 'completed this action';
          } else if (eventClass.includes('updated')) {
            iconBgClass = 'bg-yellow-50';
            iconTextClass = 'text-yellow-500';
            iconClass = 'fa-edit';
            actionText = 'updated this action';
          } else if (eventClass.includes('reminder')) {
            iconBgClass = 'bg-amber-50';
            iconTextClass = 'text-amber-500';
            iconClass = 'fa-clock';
            actionText = 'set a reminder';
          } else if (eventClass.includes('due')) {
            iconBgClass = 'bg-amber-50';
            iconTextClass = 'text-amber-500';
            iconClass = 'fa-calendar';
            actionText = 'set a due date';
          }
          
          // Format relative time
          const relativeTime = getRelativeTimeFromDate(timestamp);
          
          // Format the date and time for display
          const dateTimeFormat = parseAndFormatDateTime(timestamp);
          
          // Create the timeline event HTML with card style matching actions.html.erb
          const eventHTML = `
            <div class="flex items-start gap-3 p-2.5 bg-white rounded-lg border border-gray-100/80 hover:bg-gray-50/50 transition-colors">
              <div class="flex-shrink-0 w-8 h-8 rounded-full ${iconBgClass} flex items-center justify-center">
                <i class="fas ${iconClass} h-3.5 w-3.5 ${iconTextClass}"></i>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                  <h5 class="text-sm font-medium text-gray-800">${title}</h5>
                  <div class="flex items-center gap-2">
                    <span class="text-xs text-gray-500">${dateTimeFormat.date}</span>
                    <span class="text-xs text-gray-400">${dateTimeFormat.time}</span>
                  </div>
                </div>
                <p class="mt-0.5 text-xs text-gray-600">
                  ${creatorName} ${actionText}
                  ${comment ? `<span class="font-medium">: ${comment}</span>` : ''}
                </p>
                ${title.toLowerCase().includes('assigned') ? `
                  <div class="mt-1.5 flex items-center gap-2">
                    <span class="relative flex shrink-0 overflow-hidden h-5 w-5 border border-white shadow-sm rounded-full">
                      <div class="flex h-full w-full items-center justify-center bg-gray-200 text-xs font-medium text-gray-600">
                        ${creatorInitials}
                      </div>
                    </span>
                    <span class="text-xs text-gray-500">Assigned to team member</span>
                  </div>
                ` : ''}
              </div>
            </div>
          `;
          
          // Add the event to the timeline
          timelineContent.insertAdjacentHTML('beforeend', eventHTML);
        });
      }
    } else {
      console.error('Error loading timeline data:', xhr.status, xhr.statusText);
      
      // Show error message
      const timelineContent = timelineAccordion.querySelector('.timeline-content');
      if (timelineContent) {
        timelineContent.innerHTML = `
          <div class="py-4 text-center">
            <p class="text-sm text-red-500">Error loading timeline data. Please try again.</p>
          </div>
        `;
      }
    }
  };
  
  // Set up error handling
  xhr.onerror = function() {
    console.error('Network error when trying to fetch timeline data');
    
    // Show error message
    const timelineContent = timelineAccordion.querySelector('.timeline-content');
    if (timelineContent) {
      timelineContent.innerHTML = `
        <div class="py-4 text-center">
          <p class="text-sm text-red-500">Network error when loading timeline. Please check your connection and try again.</p>
        </div>
      `;
    }
  };
  
  // Send the request
  xhr.send();
}

/**
 * Get assigned users HTML from an event
 * @param {HTMLElement} event - The event element
 * @returns {string} - HTML for assigned users
 */
function getAssignedUsersFromEvent(event) {
  // This is a placeholder - in a real implementation, you would extract
  // assigned users from the event data
  return `
    <div class="flex flex-col items-center gap-1">
      <span class="relative flex shrink-0 overflow-hidden h-7 w-7 border-2 border-white shadow-sm rounded-full">
        <div class="flex h-full w-full items-center justify-center bg-gray-200 text-xs font-medium text-gray-600">JD</div>
      </span>
      <span class="text-xs font-medium text-gray-600">John Doe</span>
    </div>
  `;
}

/**
 * Parse and format date time string
 * @param {string} dateString - The date string (format: DD/MM/YYYY HH:MM)
 * @returns {Object} - Object with formatted date and time
 */
function parseAndFormatDateTime(dateString) {
  if (!dateString) return { date: 'N/A', time: 'N/A' };
  
  try {
    // Parse the date string (format: DD/MM/YYYY HH:MM)
    const parts = dateString.split(' ');
    const dateParts = parts[0].split('/');
    const timeParts = parts[1].split(':');
    
    const day = parseInt(dateParts[0], 10);
    const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed
    const year = parseInt(dateParts[2], 10);
    const hour = parseInt(timeParts[0], 10);
    const minute = parseInt(timeParts[1], 10);
    
    const date = new Date(year, month, day, hour, minute);
    
    // Format date as 'MMM DD, YYYY' (e.g., 'Jan 01, 2023')
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    });
    
    // Format time as 'HH:MM AM/PM' (e.g., '09:30 AM')
    const formattedTime = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
    
    return {
      date: formattedDate,
      time: formattedTime
    };
  } catch (e) {
    console.error('Error parsing date:', e);
    return { date: 'N/A', time: 'N/A' };
  }
}

/**
 * Get relative time from date string
 * @param {string} dateString - The date string
 * @returns {string} - Relative time
 */
function getRelativeTimeFromDate(dateString) {
  if (!dateString) return 'recently';
  
  try {
    // Parse the date string (format: DD/MM/YYYY HH:MM)
    const parts = dateString.split(' ');
    const dateParts = parts[0].split('/');
    const timeParts = parts[1].split(':');
    
    const day = parseInt(dateParts[0], 10);
    const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed
    const year = parseInt(dateParts[2], 10);
    const hour = parseInt(timeParts[0], 10);
    const minute = parseInt(timeParts[1], 10);
    
    const date = new Date(year, month, day, hour, minute);
    const now = new Date();
    
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (diffDays === 0) {
      if (diffHours === 0) {
        return 'just now';
      }
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays === 1) {
      return 'yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
    } else {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months !== 1 ? 's' : ''} ago`;
    }
  } catch (e) {
    console.error('Error parsing date:', e);
    return 'recently';
  }
}

/**
 * Get HTML for assigned users
 * @param {HTMLElement} actionCard - The action card element
 * @returns {string} - HTML string for assigned users
 */
function getAssignedUsersHTML(actionCard) {
  // Get assigned users from the action card
  const assignedUsers = actionCard.querySelectorAll('.flex.-space-x-2 .relative.flex.shrink-0.overflow-hidden.h-8.w-8');
  let html = '';
  
  // If no assigned users, return empty string
  if (!assignedUsers.length) return html;
  
  // Create HTML for each assigned user
  assignedUsers.forEach(user => {
    const userName = user.getAttribute('alt') || 'Team Member';
    const userInitials = userName.split(' ').map(n => n[0]).join('');
    
    html += `
      <div class="flex flex-col items-center gap-1">
        <span class="relative flex shrink-0 overflow-hidden h-7 w-7 border-2 border-white shadow-sm rounded-full">
          ${user.querySelector('img') ? user.querySelector('img').outerHTML : `
            <div class="flex h-full w-full items-center justify-center bg-gray-200 text-xs font-medium text-gray-600">
              ${userInitials || 'U'}
            </div>
          `}
        </span>
        <span class="text-xs font-medium text-gray-600">${userName}</span>
      </div>
    `;
  });
  
  return html;
}
