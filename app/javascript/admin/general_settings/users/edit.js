document.addEventListener("DOMContentLoaded", () => {
  initializeUserImagePreview();
});

const initializeUserImagePreview = () => {
  const input = document.getElementById("profileImage");
  const preview = document.getElementById("previewImage");
  const placeholder = document.getElementById("upload-placeholder");
  input.addEventListener("change", (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        preview.src = e.target.result;
        preview.classList.remove("hidden");
        placeholder.style.display = "none";
      };
      reader.readAsDataURL(file);
    }
  });
}
