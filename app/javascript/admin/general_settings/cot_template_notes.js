$(document).ready(function () {
    if (!($('#templateNoteForm').length || $('.cot-template-notes-page').length)) return;

    initializeSelect2();
    initializeFormSubmission();

    document.querySelectorAll('.toggle-popout-menu-button').forEach(button => {
        button.addEventListener('click', function (event) {
            event.preventDefault();
            event.stopPropagation();

            const noteId = button.dataset.id;
            const popout = document.querySelector(`.index-note-templates-popout[data-note-id="${noteId}"]`);

            document.querySelectorAll('.index-note-templates-popout').forEach(popout => {
                popout.classList.add('hidden');
            });

            popout.classList.toggle('hidden');
        });
    })

    document.addEventListener('click', function (event) {
        var popups = $(".index-note-templates-popout");

        popups.each(function () {
            var popup = $(this);
            if ($(event.target).closest('button').data('note-id') !== popup.data('note-id')) {
                popup.addClass('hidden');
            }
        });
    });

    $(".toggle-popout").on("click", function () {
        togglePopoutMenu("index-form-popout", $(this).data("id"));
    });

    function togglePopoutMenu(popoutClass, planId) {
        const popout = document.querySelector(`.${popoutClass}[data-note-id="${planId}"]`);
        popout.classList.toggle('hidden');
    }

    function initializeFormSubmission() {
        const form = $('#templateNoteForm');
        
        if (form.length) {
            // Add submit event listener
            form.on('submit', function(e) {
                // Sync TinyMCE content before form submission
                if (typeof tinymce !== 'undefined') {
                    const editor = tinymce.get('templateContent');
                    if (editor) {
                        editor.save();
                    }
                }
                
                // Let the form submit normally
                return true;
            });
            
            // Add direct click event on submit button as backup
            const submitBtn = form.find('input[type="submit"]');
            
            submitBtn.on('click', function(e) {
                // Sync TinyMCE content BEFORE validation
                if (typeof tinymce !== 'undefined') {
                    const editor = tinymce.get('templateContent');
                    if (editor) {
                        editor.save();
                    }
                }
                
                // Check if form is valid
                const formElement = form[0];
                if (formElement.checkValidity && !formElement.checkValidity()) {
                    return false;
                }
                
                // Let the click proceed normally
                return true;
            });
        }
    }

    function initializeSelect2() {
        if ($('#templateUsers').length) {
            const selectElement = $('#templateUsers');
            selectElement.select2({
                theme: 'tailwindcss-3',
                placeholder: 'Select user',
                allowClear: true,
                ajax: {
                    url: '/admin/general_settings/users/select2_search',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data, params) {
                        return data;
                    }
                },
                minimumInputLength: 1,
                width: '100%'
            });
        }
    }
})
