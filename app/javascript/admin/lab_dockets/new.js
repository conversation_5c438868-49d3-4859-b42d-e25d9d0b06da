$(document).ready(function () {
    if (!$('.lab-docket-form').length) return;

    initDocketTabs();

    $("#date_due").change(function(e) {
        const days = Math.max(...$(".field-lab-item").map(function() { ($(this).get(0).selectedOptions[0] ? Number($(this).get(0).selectedOptions[0].dataset["returnTime"]) : null) || 14 }).get(), 14);
        if ($("#date_due").val() < new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]) {
            $(".hide-datepicker").click();
            Swal.fire({
                title: 'Are you sure?',
                text: "You are allowed pick the due date less than 2 weeks. Do you want to change the due date?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // User confirmed, do nothing as the date is already changed
                } else {
                    // User cancelled, revert the date back to the calculated value
                    $("#date_due").val(new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]);
                }
            });
        } else if ($("#date_due").val() !== new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]) {
            $(".hide-datepicker").click();
            Swal.fire({
                title: 'Are you sure?',
                text: "Do you want to change the due date?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // User confirmed, do nothing as the date is already changed
                } else {
                    // User cancelled, revert the date back to the calculated value
                    $("#date_due").val(new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split("T")[0]);
                }
            });
        }
    });

    function calculateTimeAllowed() {
        var dateSent = new Date($('#date_sent').val());
        var dateDue = new Date($('#date_due').val());

        if (!isNaN(dateSent.getTime()) && !isNaN(dateDue.getTime())) {
            var timeDifference = (dateDue - dateSent) / (1000 * 60 * 60 * 24);
            $('#time_allowed_value').text(timeDifference + ' days');
        } else {
            $('#time_allowed_value').text('0 days');
        }
    }

    $('#date_sent, #date_due').on('change', calculateTimeAllowed);

    calculateTimeAllowed();

    $('#draft-button').on('click', function () {
        $('#ld_draft').val('1');
    });
})

function initDocketTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetId = btn.getAttribute('data-tab-target');

            // Deselect all buttons
            tabButtons.forEach(b => {
                b.setAttribute('aria-selected', 'false');
                b.classList.remove('ring-2', 'ring-gray-300', 'ring-offset-2');
            });

            // Hide all panels
            tabPanels.forEach(panel => {
                panel.hidden = true;
            });

            // Activate selected tab
            btn.setAttribute('aria-selected', 'true');
            document.getElementById(targetId).hidden = false;
        });
    });

    // Show default tab
    const defaultBtn = document.querySelector('.tab-btn[aria-selected="true"]');
    if (defaultBtn) {
        document.getElementById(defaultBtn.getAttribute('data-tab-target')).hidden = false;
    }
}
