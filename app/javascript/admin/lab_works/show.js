$(document).ready(function () {
    if (!$('.lab-work-container').length) return;

    const chatContainer = document.getElementById('lab-chat-container');
    if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    const tabTitle = document.getElementById("tabAssetsTitle");

    const tabColors = {
        1: ['bg-[#a8c7ff]', 'text-[#0055cc]'],
        2: ['bg-[#a8e6c3]', 'text-[#0a5c2d]'],
        3: ['bg-[#d8bbfd]', 'text-[#5b21b6]'],
        4: ['bg-[#ffc2d6]', 'text-[#9d174d]'],
        5: ['bg-[#ffd8a8]', 'text-[#9a3412]'],
        6: ['bg-[#fef08a]', 'text-[#854d0e]'],
        7: ['bg-[#a5f3fc]', 'text-[#0e7490]'],
    };

    function clearTabs() {
        tabButtons.forEach(btn => {
            btn.classList.remove(...Object.values(tabColors).flat());
            btn.classList.add('bg-[#f2f2f7]', 'text-[#86868b]');
        });

        tabPanes.forEach(pane => {
            pane.style.display = 'none';
        });
    }

    tabButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabId = btn.getAttribute('data-tab');
            const targetId = btn.getAttribute('data-tab-target');
            const targetPane = document.getElementById(targetId);
            if (!targetPane) return;

            const newTitle = btn.getAttribute("data-title");
            if (newTitle && tabTitle) {
                tabTitle.textContent = newTitle;
            }

            clearTabs();

            btn.classList.remove('bg-[#f2f2f7]', 'text-[#86868b]');
            btn.classList.add(...tabColors[tabId]);

            targetPane.style.display = 'block';
        });
    });
});
