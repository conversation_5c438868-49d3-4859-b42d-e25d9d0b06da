<div>
  <% if collection.empty? %>
    <div class="bg-white p-8 rounded-2xl shadow text-center">
      <p class="text-gray-500 text-[14px]"><%= options[:empty_message] %></p>
    </div>
  <% else %>
    <table class="<%= options[:table_class] %> table-auto border-separate" style="border-spacing: 0 12px;">
      <thead>
        <tr>
          <% columns.each do |column| %>
            <th class="<%= options[:header_class] %> <%= column[:class] if column[:class] %> px-3 py-2 text-left font-medium bg-transparent">
              <%= column[:label] %>
            </th>
          <% end %>
          <% if options[:show_actions] %>
            <th class="<%= options[:header_class] %> px-3 py-2 text-right font-medium bg-transparent">Actions</th>
          <% end %>
        </tr>
      </thead>
      <tbody>
        <% collection.each do |record| %>
          <%
            # Generate data attributes if row_data option is provided
            data_attrs = {}
            if options[:row_data].is_a?(Proc)
              data_attrs = options[:row_data].call(record) || {}
            end

            # Convert data attributes to HTML attributes with proper escaping
            data_attr_html = data_attrs.map { |key, value| "#{key}=\"#{html_escape(value)}\"" }.join(' ')
          %>
          <tr class="admin-table-row <%= options[:row_class] %>" <%= data_attr_html.html_safe if data_attr_html.present? %>>
            <% columns.each_with_index do |column, index| %>
              <td class="<%= options[:cell_class] %> <%= column[:class] if column[:class] %> px-3 py-3 <%= 'rounded-l-xl' if index == 0 %> <%= 'rounded-r-xl' if index == columns.length - 1 && !options[:show_actions] %> bg-white">
                <% if column[:content] %>
                  <%= capture { column[:content].call(record) } %>
                <% elsif column[:attribute] %>
                  <%= record.send(column[:attribute]) %>
                <% elsif column[:method] %>
                  <%= send(column[:method], record) %>
                <% end %>
              </td>
            <% end %>
            <% if options[:show_actions] %>
              <td class="<%= options[:actions_class] %> px-3 py-3 rounded-r-xl bg-white text-right">
                <% if block_given? %>
                  <%= capture(record, &block) %>
                <% end %>
              </td>
            <% end %>
          </tr>
        <% end %>
      </tbody>
    </table>
  <% end %>

  <div class="md:hidden space-y-3">
    <% if collection.empty? %>
      <div class="bg-white p-8 rounded-2xl shadow text-center">
        <p class="text-gray-500 text-lg"><%= options[:empty_message] %></p>
      </div>
    <% else %>
      <% collection.each do |record| %>
        <div class="<%= options[:row_class] %> p-4">
          <% columns.each do |column| %>
            <% next if column[:mobile_hidden] %>
            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <span class="<%= options[:header_class] %>"><%= column[:label] %></span>
              <span class="<%= options[:cell_class] %> <%= column[:class] if column[:class] %>">
                <% if column[:content] %>
                  <%= capture { column[:content].call(record) } %>
                <% elsif column[:attribute] %>
                  <%= record.send(column[:attribute]) %>
                <% elsif column[:method] %>
                  <%= send(column[:method], record) %>
                <% end %>
              </span>
            </div>
          <% end %>
          <% if options[:show_actions] && block_given? %>
            <div class="mt-4 pt-4 border-t border-gray-100">
              <div class="<%= options[:actions_class] %>">
                <%= capture(record, &block) %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
