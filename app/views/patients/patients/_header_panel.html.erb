<div class="patient-header-block flex items-center gap-3 bg-white/70 backdrop-blur-lg p-1.5 rounded-full shadow-md border border-white/50">
  <button class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background
    transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
    isabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0
    hover:text-accent-foreground rounded-full w-9 h-9 hover:bg-slate-200/70" type="button" id="radix-«rk»" aria-haspopup="menu"
          aria-expanded="false" data-state="closed">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell w-[24px] h-[24px] text-slate-500">
      <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
      <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
    </svg>
    <span class="sr-only">Notifications</span>
  </button>
  <div class="settings-dropdown-wrapper relative">
    <button type='button' class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background
    transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
    disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0
    hover:text-accent-foreground rounded-full w-9 h-9 hover:bg-slate-200/70">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings w-[24px] h-[24px] text-slate-500">
        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0
        0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0
        0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0
        1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2
        0 0 0-2-2z"></path>
        <circle cx="12" cy="12" r="3"></circle>
      </svg>
      <span class="sr-only">Settings</span>
    </button>

    <div class="settings-dropdown-menu absolute right-0 mt-2 w-52 bg-white border border-gray-200 rounded shadow-lg hidden z-10">
      <%= link_to "Update Personal Details", edit_patients_patient_path(@patient),
                  class: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>

      <button type="button" class="change-password-btn block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        Change Password
      </button>
    </div>
  </div>
  <div class="relative" data-patient-image-wrapper>
    <%= image_tag(
          (@patient.image.attached? ? url_for(@patient.image) : "default-avatar.webp"),
          class: "rounded-full w-[36px] h-[36px] cursor-pointer",
          data: { dropdown: true }
        ) %>

    <div data-dropdown-menu class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded shadow-lg hidden z-10">
      <button type="button" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" data-change-image>
        Change Image
      </button>

      <%= link_to "Logout", destroy_patient_session_path, method: :delete, class: "block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-gray-100" %>
    </div>

    <form action="<%= patients_patient_path(@patient) %>" method="post" enctype="multipart/form-data" data-patient-image-form>
      <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
      <%= hidden_field_tag :_method, :patch %>
      <input type="file" name="patient[image]" id="patient_image_<%= @patient.id %>" accept="image/*" class="hidden" data-patient-image-input />
    </form>
  </div>
  <%#= image_tag((@patient.image.attached?) ? @patient.image.url : "default-avatar.webp", class: "rounded-full w-[36px] h-[36px]") %>
</div>

<div id="changePasswordModal" class="hidden fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4">
  <div class="bg-white rounded-xl shadow-lg w-full max-w-md relative p-4">
    <div class="modal-header flex items-center justify-between mb-3">
      <h2 class="text-xl font-semibold">Change Password</h2>
    </div>

    <div class="modal-body">
      <form id="changePasswordForm" class="space-y-4">
        <div>
          <label for="newPassword" class="block text-sm font-medium">New Password</label>
          <input type="password" id="newPassword" name="newPassword" required class="w-full border rounded px-3 py-2 mt-1" />
        </div>

        <div>
          <label for="verificationCode" class="block text-sm font-medium">Verification Code</label>
          <div class="flex gap-2">
            <input type="text" id="verificationCode" name="verificationCode" required class="flex-1 border rounded px-3 py-2 mt-1" />
            <button type="button" id="sendCodeBtn" class="text-sm text-blue-600 hover:underline whitespace-nowrap">Send Code</button>
          </div>
        </div>

        <div class="text-right">
          <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Submit</button>
          <button type="button" id="cancelChangePassword" class="ml-2 text-gray-500 hover:underline">Cancel</button>
        </div>
      </form>
    </div>
  </div>
</div>
