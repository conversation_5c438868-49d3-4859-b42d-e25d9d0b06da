<%= form_with(
      model: @role,
      url: @role.new_record? ? admin_general_settings_roles_path : admin_general_settings_role_path(@role),
      method: @role.new_record? ? :post : :patch,
      local: true,
      id: "role-form"
    ) do |f| %>


  <div class="bg-white rounded-xl border border-slate-200 p-6 mb-6">
    <h2 class="text-lg font-semibold text-slate-900 mb-4">Role Details</h2>
    
    <div class="space-y-4">
      <div>
        <%= f.label :name, "Role Name", class: "block text-sm font-medium text-slate-700 mb-2" %>
        <%= f.text_field :name, class: "max-w-md px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors", required: true %>
      </div>
    </div>
  </div>


  <div class="space-y-6">
    <div>
      <h2 class="text-lg font-semibold text-slate-900 mb-1">Permissions</h2>
      <p class="text-sm text-slate-600 mb-6">Select the permissions this role should have.</p>
    </div>

    <div class="space-y-6">
      <% PERMISSIONS.each do |group_key, group| %>
        <div class="bg-white rounded-xl border border-slate-200 p-6">

          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">

              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer group-toggle" data-group="<%= group_key %>">
                <div class="relative w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-500"></div>
              </label>
              <h3 class="text-base font-medium text-slate-900"><%= group[:label] %></h3>
            </div>
            <label class="flex items-center gap-2 cursor-pointer select-all-btn" data-group="<%= group_key %>">
              <input type="checkbox" class="w-4 h-4 green-checkbox select-all-checkbox" data-group="<%= group_key %>">
              <span class="text-sm text-blue-600 hover:text-blue-700">Select All</span>
            </label>
          </div>


          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% group[:activities].each do |activity_key, attrs| %>
              <% next if group_key == 'ticket' && activity_key == 'index_team' && !policy(Team).index? %>
              <label class="flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 cursor-pointer transition-colors">
                <%= check_box_tag 'role[permissions][]',
                                  attrs[:id],
                                  @role.permissions.include?(attrs[:id]),
                                  id: "role_permissions_#{attrs[:id]}",
                                  class: 'w-4 h-4 permission-checkbox green-checkbox',
                                  data: { group: group_key } %>
                <span class="text-sm text-slate-700"><%= attrs[:label] %></span>
              </label>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
