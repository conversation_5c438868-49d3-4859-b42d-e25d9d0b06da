<!-- ROLE SELECTION -->
<h2 class="text-lg font-semibold text-slate-800 mb-4">Roles and practices</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
  <div>
    <%= f.label :role, class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.select :role, options_for_select(
                          Role.all.map { |role| [role.name, role.name.parameterize(separator: '_')] },
                          @user.roles.first&.name&.parameterize(separator: '_')
                        ),
      {},
      class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
      %>
  </div>
  <div>
    <div class="block mb-1 font-semibold text-slate-700">Practices</div>
    <div id="practices-multiselect" class="multi-select w-full relative group w-full rounded-xl bg-slate-50 p-6 text-sm min-h-[140px]">
      <div class="selected-container flex flex-wrap gap-2 mb-1">
        <% @practices.each do |practice| %>
          <% is_selected = @user.practice_ids.include?(practice.id) %>
          <div
            data-id="<%= practice.id %>"
            class="selected-item flex items-center gap-2 rounded-lg bg-white py-1.5 pl-2 pr-3 border border-slate-200 shadow-sm text-sm text-slate-800 <%= is_selected ? '' : 'hidden' %>"
          >
            <img
              alt="<%= practice.name %>"
              loading="lazy"
              width="24"
              height="24"
              decoding="async"
              class="rounded-md"
              src="<%= practice.logo&.url || 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/generic-healthcare-logo-aAabdVS1qJEljiszdYf65uiReZ2XOT.png' %>"
              style="color: transparent;"
            />
            <span class="font-medium flex-grow"><%= practice.name %></span>
            <button
              type="button"
              aria-label="Remove <%= practice.name %>"
              class="rounded-full p-0.5 text-slate-500 hover:bg-slate-100 hover:text-slate-800 remove-btn"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x !h-4 !w-4"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>
            </button>
            <input type="hidden" name="user[practice_ids][]" value="<%= practice.id %>" />
          </div>
        <% end %>
      </div>

      <div class="no-selected text-sm text-gray-500 italic mb-2" style="<%= @user.practice_ids.any? ? 'display:none' : '' %>">
        No practices selected
      </div>

      <div
        class="options-container max-h-48 overflow-y-auto rounded-md hidden absolute z-10 bg-white w-full"
        style="top: 100%; left: 0;"
      >
        <% @practices.each do |practice| %>
          <% selected = @user.practice_ids.include?(practice.id) %>
          <div
            data-id="<%= practice.id %>"
            data-name="<%= practice.name.strip.downcase %>"
            class="practice-option cursor-pointer px-4 hover:bg-blue-100 <%= selected ? "hidden" : "" %> flex items-center gap-3 h-[50px]"
          >
            <img
              alt="<%= practice.name %>"
              loading="lazy"
              width="24"
              height="24"
              decoding="async"
              class="rounded-md flex-shrink-0"
              src="<%= practice.logo&.url || 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/attachments/gen-images/public/generic-healthcare-logo-aAabdVS1qJEljiszdYf65uiReZ2XOT.png' %>"
              style="color: transparent;"
            />
            <span class="text-sm font-medium text-slate-700"><%= practice.name %></span>
          </div>
        <% end %>
      </div>
      <div class="relative mt-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-search absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </svg>
        <div class="flex items-center border-b border-b-[#e4e4e7] px-3" cmdk-input-wrapper="">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-search mr-2 h-4 w-4 shrink-0 opacity-50"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
          <input
            class="search-input flex py-3 text-sm outline-hidden placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md border-slate-200 !bg-white pl-9 h-10 focus:outline-none"
            placeholder="Search practices..."
            cmdk-input=""
            autocomplete="off"
            autocorrect="off"
            spellcheck="false"
            aria-autocomplete="list"
            role="combobox"
            aria-expanded="true"
            aria-controls="«r1»"
            aria-labelledby="«r2»"
            id="«r3»"
            type="text"
            value=""
          />
        </div>
      </div>
    </div>
  </div>
</div>
