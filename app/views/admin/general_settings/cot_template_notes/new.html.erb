<% content_for :title, "New COT Template Note" %>

<main class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area min-h-[calc(100vh-56px)] bg-gray-50 cot-template-notes-page">
  <%= render 'admin/general_settings/side_panel' %>

  <div class="flex-grow">
    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-slate-800">New COT Template Note</h1>
          <p class="text-sm text-slate-600 mt-1">Create a new course of treatment template note</p>
        </div>
        <%= link_to admin_general_settings_cot_template_notes_path,
                    class: "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-white hover:bg-gray-50 text-slate-700 border border-slate-200 shadow-sm font-medium px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md" do %>
          <span class="material-symbols-outlined">arrow_back</span>
          Back to Templates
        <% end %>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <%= form_with model: @cot_template_note,
                      url: admin_general_settings_cot_template_notes_path,
                      method: :post,
                      local: true,
                      id: "templateNoteForm",
                      class: "space-y-6" do |form| %>

          <div class="p-6 space-y-6">
            <div>
              <label for="templateName" class="block text-sm font-medium text-gray-700 mb-2">Template Name</label>
              <%= form.text_field :title,
                                  id: "templateName",
                                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",
                                  placeholder: "Enter template name",
                                  required: true %>
            </div>

            <div>
              <label for="templatePractice" class="block text-sm font-medium text-gray-700 mb-2">Practice</label>
              <%= form.select :practice_id,
                              options_from_collection_for_select(@practices, :id, :name, Current.practice_id),
                              { prompt: "Select Practice" },
                              { id: "templatePractice",
                                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white",
                                required: true } %>
            </div>

            <div>
              <label for="templateUsers" class="block text-sm font-medium text-gray-700 mb-2">User</label>
              <%= form.select :user_id,
                              [],
                              { prompt: "Select user" },
                              { id: "templateUsers",
                                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white" } %>
            </div>

            <div>
              <label for="templateContent" class="block text-sm font-medium text-gray-700 mb-2">Template Content</label>
              <%= form.text_area :content,
                                 id: "templateContent",
                                 class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white tinymce_editor",
                                 rows: 12,
                                 placeholder: "Enter template content...",
                                 required: true %>
            </div>
          </div>

          <div class="px-6 py-4 bg-gray-50 rounded-b-xl border-t border-gray-100 flex justify-end space-x-3">
            <%= link_to "Cancel",
                        admin_general_settings_cot_template_notes_path,
                        class: "px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 transition-colors duration-200" %>
            <%= form.submit "Create Template",
                            class: "px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-full hover:bg-blue-700 transition-colors duration-200" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</main>
