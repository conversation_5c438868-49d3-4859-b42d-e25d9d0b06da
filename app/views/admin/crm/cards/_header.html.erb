<header class="bg-white border-b border-gray-200 sticky top-0 z-10">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <div class="flex items-center">
        <a href="<%= admin_crm_board_path(@board) %>"
          class="inline-flex items-center px-3 py-2 rounded-full bg-gray-100 text-gray-700 text-[14px] hover:bg-gray-200 transition-colors mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-chevron-left w-4 h-4 mr-1">
            <path d="m15 18-6-6 6-6"></path>
          </svg>Back to Board
        </a>
        <h1 class="text-lg font-medium">Patient Details</h1>
      </div>
      <div class="flex items-center space-x-3">
        <% if card.patient.present? %>
          <a href="<%= new_admin_patient_payment_path(card.patient) %>"
            class="px-4 py-2 text-sm rounded-full bg-green-100 text-green-700 shadow-sm hover:bg-green-200 transition-colors flex items-center border border-green-200">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-tag w-4 h-4 mr-2">
              <path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path>
              <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
            </svg>Take Payment
          </a>
          <button type="button"
                  class="px-4 py-2 text-sm rounded-full bg-blue-100 text-blue-700 shadow-sm hover:bg-blue-200 transition-colors flex items-center border border-blue-200 book-appointment-button"
                  data-patient-id="<%= card.patient.id %>"
                  data-practice-id="<%= @board.practice_id %>">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-calendar w-4 h-4 mr-2">
              <path d="M8 2v4"></path>
              <path d="M16 2v4"></path>
              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
              <path d="M3 10h18"></path>
            </svg>Book Appointment
          </button>
        <% end %>
      </div>
    </div>
  </div>
</header>
