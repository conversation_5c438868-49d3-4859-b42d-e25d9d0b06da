<div class="min-h-[calc(100%-56px)] bg-[#f5f5f7] text-[#1d1d1f] font-sans">
  <div class="w-full px-6 pt-4 lab-work-container" data-labwork-id="<%= @lab_work.id %>">
    <h1 class="text-2xl font-semibold mb-4 tracking-tight flex items-center">
      <%= "#{@lab_work.patient.full_name}'s lab work" %>
    </h1>

    <div class="mb-4">
      <% is_error = @lab_work.status == "Lab Error - Adjustment or Remake Required" %>
      <% current_index = is_error ? lab_work_steps.size - 1 : lab_work_steps.index { |s| s[:key] == current_step_key(@lab_work.status) } %>

      <div class="flex items-center justify-between mb-2">
        <% lab_work_steps.each_with_index do |_step, index| %>
          <% is_active = index <= current_index %>
          <% color_class = is_error ? '#f44336' : '#4caf50' %>
          <% border_class = is_active ? "bg-[#{color_class}] text-white" : 'bg-white border-2 border-[#d2d2d7]' %>
          <% dot_class = is_active ? 'bg-white' : 'bg-[#d2d2d7]' %>

          <div class="relative flex items-center justify-center w-6 h-6 rounded-full <%= border_class %> z-10">
            <span class="h-2 w-2 rounded-full <%= dot_class %>"></span>
          </div>

          <% unless index == lab_work_steps.size - 1 %>
            <% line_color = index < current_index ? color_class : '#d2d2d7' %>
            <div class="flex-1 h-1 bg-[<%= line_color %>]"></div>
          <% end %>
        <% end %>
      </div>

      <div class="flex justify-between text-sm text-[#6e6e73]">
        <div class="w-1/4 text-left">Sent To Lab</div>
        <% if @lab_work.checked_in_by.present? %>
          <div class="w-1/4 text-center mr-20">
            <p class="mb-2">Arrived at Practice. Checked-In By</p>

            <div class="flex flex-center justify-center gap-2">
              <span class="relative flex shrink-0 overflow-hidden rounded-full w-7 h-7 border border-[#e5e5e7] shadow-sm bg-gradient-to-b from-[#f5f5f7] to-[#e8e8ed] p-[1px]">
                <%= image_tag @lab_work.checked_in_by.image.attached? ? @lab_work.checked_in_by.image : "default-avatar.webp", class: "rounded-full" %>
              </span>
              <div class="text-xs text-[#0071e3] font-medium bg-[#f5f9ff] px-2 py-1 rounded-md border border-[#e1eeff] inline-block"><%= @lab_work.checked_in_by&.full_name %></div>
            </div>
          </div>
        <% else %>
          <div class="w-1/4 text-center mr-20">Awaiting Check-In</div>
        <% end %>

        <% if @lab_work.quality_checked_by.present? %>
          <div class="w-1/4 text-center ml-25">
            <p class="mb-2">Quality Checked By</p>

            <div class="flex flex-center justify-center gap-2">
              <span class="relative flex shrink-0 overflow-hidden rounded-full w-7 h-7 border border-[#e5e5e7] shadow-sm bg-gradient-to-b from-[#f5f5f7] to-[#e8e8ed] p-[1px]">
                <%= image_tag @lab_work.quality_checked_by.image.attached? ? @lab_work.quality_checked_by.image : "default-avatar.webp", class: "rounded-full" %>
              </span>
              <div class="text-xs text-[#0071e3] font-medium bg-[#f5f9ff] px-2 py-1 rounded-md border border-[#e1eeff] inline-block"><%= @lab_work.quality_checked_by&.full_name %></div>
            </div>
          </div>
        <% else %>
          <div class="w-1/4 text-center ml-25">Awaiting Quality Check</div>
        <% end %>
        <div class="w-1/4 text-right"><%= @lab_work.status == "Lab Error - Adjustment or Remake Required" ? "Remake" : "Fitted & Archived" %></div>
      </div>
    </div>

    <div class="flex gap-6 w-full mt-4">
      <div class="flex-1 flex flex-col h-[calc(100vh-6rem)]">
        <!-- Card Profile -->
        <%= render 'admin/lab_works/lab_work_information' %>

        <!-- Lab Work Conversation -->
        <% if @conversation %>
        <div class="" data-v0-t="card">
          <div class="">
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 h-full flex flex-col">
              <!-- Header -->
              <div class="border-b border-gray-100 p-4">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">Lab Work Notes</h3>
                  <div class="flex items-center space-x-2">
                    <div class="h-3 w-3 bg-amber-500 rounded-sm"></div>
                    <span class="font-medium text-sm">
                      <%= current_user.lab_user? ? 'Lab communication' : 'Internal notes only' %>
                    </span>
                  </div>
                </div>
              </div>

              <div class="flex-1 overflow-y-auto p-4" id="lab-chat-container" style="max-height: 400px;">
                <div id="lab-chat-messages" class="space-y-6 flex flex-col">
                  <% @messages.each do |message| %>
                    <%
                      # For lab_user: messages from lab_user authors are outbound (their own), messages from non-lab_user authors are inbound (from others)
                      # For regular users: messages from lab_user authors are inbound (from others), messages from non-lab_user authors are outbound (their own)
                      if current_user.lab_user?
                        is_outbound = message.author&.lab_user? == false
                      else
                        is_outbound = message.author&.lab_user? == true
                      end
                    %>
                    <div class="<%= is_outbound ? 'flex' : 'flex justify-end' %> mb-6">
                      <div class="max-w-[75%] relative">
                        <div class="flex <%= is_outbound ? '' : 'flex-row-reverse' %> items-end">
                          <div class="relative">
                            <span class="relative flex shrink-0 h-8 w-8 <%= is_outbound ? 'mr-2' : 'ml-2' %> rounded-full overflow-hidden">
                              <% if message.author %>
                                <%= render 'layouts/shared/user_avatar', user: message.author, width: 32, height: 32 %>
                              <% else %>
                                <div class="h-full w-full flex items-center justify-center bg-gray-200 text-gray-700 text-xs font-medium">
                                  LW
                                </div>
                              <% end %>
                            </span>
                          </div>

                          <div class="<%= is_outbound ? '' : 'relative' %>">
                            <div class="relative group">
                              <div class="bg-amber-100 p-3 rounded-2xl <%= is_outbound ? 'rounded-bl-none' : 'rounded-br-none' %> text-sm message-bubble">
                                <div class="message-content">
                                  <%= simple_format message.content %>
                                </div>
                              </div>
                            </div>

                            <div class="flex items-center gap-2 mt-1.5 ml-3">
                              <div class="text-xs text-gray-500 font-medium"><%= message.formatted_timestamp %></div>
                              <% if message.author %>
                                <div class="text-xs text-gray-400">by <%= message.author.full_name %></div>
                              <% end %>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
              <div class="border-t border-gray-100 relative">
                <div class="p-3">
                  <%= form_with(url: create_message_admin_conversation_path(@conversation), method: :post, id: 'lab-message-form', class: 'flex flex-col') do |f| %>
                    <%= f.hidden_field :message_type, value: 'internal' %>
                    <%= f.hidden_field :lab_work_id, value: @lab_work.id %>

                    <div class="mb-3">
                      <%= f.text_area :content, id: 'lab-message-editor', class: 'w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500', placeholder: 'Add a note about this lab work...', rows: 3 %>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="h-3 w-3 bg-amber-500 rounded-sm"></div>
                        <span class="text-xs text-gray-600">
                          <%= current_user.lab_user? ? 'Lab note' : 'Internal note' %>
                        </span>
                      </div>
                      <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 py-2 bg-amber-400 hover:bg-amber-500 text-white rounded-full px-4">
                        <%= current_user.lab_user? ? 'Send Lab Note' : 'Save Note' %>
                      </button>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
        <% end %>
      </div>

      <div class="w-[25%] min-w-[280px] flex flex-col h-[calc(100vh-6rem)] overflow-auto rounded-2xl">
        <div class="bg-white/40 backdrop-blur-sm rounded-2xl border border-[#e5e5e7]/50 shadow-[0_2px_10px_rgba(0,0,0,0.02)] flex flex-col">
          <%= render "admin/lab_works/patient_assets" %>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render "admin/lab_works/update_invoice_modal" %>

<div id="dentistModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-lg">
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Lab-Work Check In</h3>
      <button id="closeDentistModal" class="text-gray-400 hover:text-gray-600">&times;</button>
    </div>
    <div class="p-4">
      <label for="dentistSelector" class="block mb-2 text-sm font-medium text-gray-700">Select Dentist</label>
      <select id="dentistSelector" class="w-full px-3 py-2 border rounded-md text-sm border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
        <% @practitioners.each do |practitioner| %>
          <option value="<%= practitioner.id %>"><%= practitioner.full_name_with_email %></option>
        <% end %>
      </select>
    </div>
    <div class="flex justify-end gap-2 p-4 border-t border-gray-200">
      <button id="closeDentistModalFooter" class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
      <button id="saveDentist" class="px-4 py-2 text-sm text-white bg-blue-600 rounded hover:bg-blue-700">Save</button>
    </div>
  </div>
</div>

<!--<div id="docketPrintModal" class="fixed inset-0 z-50 hidden items-center justify-center">-->
<div id="docketPrintModal" class="fixed inset-0 bg-black/50 z-[1100] backdrop-blur-sm hidden flex items-center justify-center">
  <div class="bg-slate-50 w-full max-w-5xl mx-auto rounded-lg shadow-lg mt-10">
    <div class="flex justify-end p-2">
      <button type="button"
              class="text-gray-500 hover:text-gray-700 text-2xl leading-none"
              data-modal-close="docketPrintModal"
              aria-label="Close">
        &times;
      </button>
    </div>
    <div class="p-0">
      <div id="print_loader" class="flex items-center justify-center h-[400px]">
        <i class="fas fa-spinner fa-spin text-xl"></i>
      </div>
      <iframe id="docket_print" class="w-full h-[900px] hidden"></iframe>
    </div>
  </div>
</div>
