<button type="button" data-tab-target="photos" data-title="Clinical Images" data-tab="4" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <%= image_tag 'ui/Cam.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="xray" data-title="X-Rays" data-tab="1" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <%= image_tag 'ui/x-ray.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="stl" data-title="STLs" data-tab="5" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <%= image_tag 'ui/cube.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="opg" data-title="OPGs" data-tab="3" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <%= image_tag 'ui/2d_view.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="cbct" data-title="CBCTs" data-tab="2" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <%= image_tag 'ui/3d_view.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="dockets" data-title="Lab Dockets" data-tab="6" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea] bg-[#fef08a] text-[#854d0e]">
  <%= image_tag 'ui/lab-work.svg', style: "width: 22px" %>
</button>
<button type="button" data-tab-target="logs" data-title="Timeline" data-tab="7" class="tab-btn cursor-pointer flex items-center justify-center w-12 h-12 rounded-full transition-colors focus:outline-none bg-[#f2f2f7] text-[#86868b] hover:bg-[#e5e5ea]">
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-history h-5 w-5">
    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
    <path d="M3 3v5h5"></path>
    <path d="M12 7v5l4 2"></path>
  </svg>
</button>
