<div class="flex items-center justify-between mb-6">
  <div class="flex items-center">
    <h1 class="text-2xl font-medium">Labwork</h1>
    <button type="button" data-practitioner-id="<%= current_user.id %>" class="ml-4 flex items-center px-4 py-1.5 rounded-full text-sm font-medium transition-all border bg-gradient-to-b
      from-white to-gray-50 shadow-md hover:shadow-lg border-gray-200 text-gray-800 cursor-pointer focus-on-me-btn" aria-pressed="false">
      <div class="w-6 h-6 rounded-full overflow-hidden mr-2 flex-shrink-0 flex items-center justify-center shadow-sm border border-gray-300 bg-gray-50">
        <%= image_tag current_user.image.attached? ? current_user.image : "default-avatar.webp", class: "w-full h-full object-cover" %>
      </div>
      <span>Focus on me</span>
    </button>
  </div>

  <div class="flex items-center space-x-4">
    <div class="relative">
      <input placeholder="Search patients..." class="w-64 px-4 py-2 pl-10 text-sm rounded-full bg-white shadow-sm focus:outline-none focus:ring-2
        focus:ring-blue-500" type="text" id="lw-patients-search">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
    </div>
    <button type="button" class="cursor-pointer px-4 py-2 text-sm rounded-full bg-white shadow-sm flex items-center relative" data-action="open-filter">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 mr-2">
        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
      </svg>
      <span>Filters</span>
      <span id="active-filter-count" class="ml-1 w-4 h-4 rounded-full bg-blue-500 text-white text-[10px] flex items-center justify-center hidden">0</span>
    </button>
    <%= link_to new_admin_lab_work_path(patient_id: patient&.id, practice_id: patient&.current_practice&.id || patient&.practices&.first&.id),
                class: 'px-4 py-2 text-sm rounded-full bg-teal-400 text-white shadow-sm flex items-center hover:bg-teal-500 transition-colors',
                style: 'text-decoration: none;' do %>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
        <path d="M5 12h14"></path>
        <path d="M12 5v14"></path>
      </svg>
      <span class="text-white">Create New Lab Docket</span>
    <% end %>
  </div>
</div>

<%= render 'admin/lab_works/filter_modal' %>

