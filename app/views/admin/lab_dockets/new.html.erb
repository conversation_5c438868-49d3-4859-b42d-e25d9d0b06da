<div class="min-h-[calc(100%-56px)] bg-gray-100 p-2 md:p-4 lab-docket-form">
  <%= form_for [:admin, @lab_docket] do |form| %>
    <%= form.hidden_field(:id, value: @lab_docket.id) %>
    <%= form.hidden_field(:lab_work_id, value: params[:lab_work_id] || @lab_docket.lab_work.id) %>
    <input type="hidden" name="draft" id="ld_draft" value="0" />

    <div class="w-full">
      <header class="mb-6"></header>
      <div class="flex flex-col md:flex-row gap-6">
        <div class="md:shrink-0">
          <div class="flex flex-col items-center gap-6 py-6 px-3 bg-gray-50 rounded-2xl shadow-sm">
            <div class="flex flex-col items-center gap-2">
              <button type="button"
                      id="lab-docket-explanation-tab"
                      role="tab"
                      aria-selected="true"
                      aria-controls="lab-docket-explanation"
                      data-tab-target="lab-docket-explanation"
                      class="tab-btn flex items-center justify-center w-14 h-14 rounded-full transition-all bg-blue-100 hover:ring-1
                      hover:ring-gray-200 hover:ring-offset-1 cursor-pointer aria-selected:ring-2 aria-selected:ring-gray-300 aria-selected:ring-offset-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info h-5 w-5">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </button>
              <span class="text-sm text-gray-600 font-medium">Information</span>
            </div>

            <div class="flex flex-col items-center gap-2">
              <button type="button"
                      id="lab-docket-prescription-tab"
                      role="tab"
                      aria-selected="true"
                      aria-controls="lab-docket-prescription"
                      data-tab-target="lab-docket-prescription"
                      class="tab-btn flex items-center justify-center w-14 h-14 rounded-full transition-all bg-rose-100 hover:ring-1 hover:ring-gray-200 hover:ring-offset-1 cursor-pointer aria-selected:ring-2 aria-selected:ring-gray-300 aria-selected:ring-offset-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                     stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pen-tool h-5 w-5">
                  <path d="M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z"></path>
                  <path d="m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18"></path>
                  <path d="m2.3 2.3 7.286 7.286"></path>
                  <circle cx="11" cy="11" r="2"></circle>
                </svg>
              </button>
              <span class="text-sm text-gray-600 font-medium">Prescription</span>
            </div>
          </div>
        </div>
        <div id="lab-docket-explanation"
             role="tabpanel"
             aria-labelledby="lab-docket-explanation-tab"
             hidden
             class="tab-panel flex-1 rounded-2xl shadow-sm p-3 md:p-5 mb-20">
          <div class="-mt-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <%= render 'admin/lab_dockets/general_info', form: form %>
              <%= render 'admin/lab_dockets/docket_info', form: form %>
            </div>
            <div class="flex flex-wrap gap-6 mt-6">
              <div class="w-[calc(25%-12px)] bg-gray-700 rounded-xl shadow-md p-6 relative overflow-hidden">
                <div class="text-gray-400 text-5xl font-light opacity-30 absolute bottom-4 right-4">Disinfection</div>
              </div>
              <div class="w-[calc(75%-12px)] bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Disinfected impression with:</label>
                    <%= form.text_field :disinfected_with,
                                        class: "w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 #{@show.present? ? 'bg-gray-100' : '' }",
                                        placeholder: "Enter disinfectant used",
                                        disabled: @show.present?
                    %>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Disinfected by:</label>
                    <%= form.select :disinfected_by_id,
                                    @clinicians.map { |u| ["#{u.full_name}", u.id] },
                                    { include_blank: "Select" },
                                    class: "w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 #{@show.present? ? 'bg-gray-100' : 'bg-white' }",
                                    disabled: @show.present?%>
                  </div>
                  <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Disinfectant further information:</label>
                    <%= form.text_area :disinfectant_further_information,
                                       class: "w-full h-24 border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none #{@show.present? ? 'bg-gray-100' : '' }",
                                       placeholder: "Enter additional information about disinfection process",
                                       disabled: @show.present? %>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Made available to lab:</label>
                    <%= form.select :made_available,
                                    ["Physical impression", "Intra oral scan", "Clinical photos", "Other"],
                                    { include_blank: "Select" },
                                    class: "w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 #{@show.present? ? 'bg-gray-100' : 'bg-white' }",
                                    disabled: @show.present?%>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div id="lab-docket-prescription"
             role="tabpanel"
             aria-labelledby="lab-docket-prescription-tab"
             hidden
             class="tab-panel flex-1 rounded-2xl shadow-sm p-3 md:p-5">
          <div>
            <div class="text-sm -mt-4">
              <div class="mb-6">
                <div class="bg-white rounded-xl shadow-md p-6 border border-gray-100 relative overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-transparent pointer-events-none"></div>

                  <div class="space-y-8 relative z-0">
                    <div>
                      <div class="flex items-center justify-between mb-8">
                        <div class="ld-container ld-treatments-container flex items-center space-x-3" data-show="<%= @show %>">
                          <ul class="flex flex-wrap items-center gap-3 nav nav-tabs" id="item_tabs">
                            <% if controller.action_name == 'new' %>
                              <li style="order: 2;">
                                <%= link_to_add_association form, :lab_docket_items,
                                                            class: 'w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 shadow-sm hover:shadow',
                                                            'data-association-insertion-node': "#lab_docket_items",
                                                            'data-association-insertion-method': "append" do %>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-5 h-5 text-gray-700">
                                    <path d="M5 12h14"></path>
                                    <path d="M12 5v14"></path>
                                  </svg>
                                <% end %>
                              </li>
                            <% end %>
                          </ul>
                        </div>
                      </div>

                      <div id="lab_docket_items">
                        <%= form.fields_for :lab_docket do |ld| %>
                          <%= form.fields_for :lab_docket_items do |builder| %>
                            <%= render 'lab_docket_item_fields', f: builder %>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <% if @show.blank? %>
        <div class="sticky-footer mt-8 flex justify-end gap-3">
          <%= link_to 'Cancel', admin_lab_works_path,
                      class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium
                ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
                focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none
                [&_svg]:size-4 [&_svg]:shrink-0 border h-10 px-4 py-2 border-pink-100 bg-pink-50 text-pink-700 hover:bg-pink-100
                 hover:text-pink-800' %>
          <%# if @lab_docket.id == nil %>
            <%#= form.submit "Save as draft", class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium
            ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
            focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4
            [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-200 text-blue-800 hover:bg-blue-300 hover:text-blue-900 cursor-pointer', id: 'draft-button' %>
          <%# end %>
          <%= form.submit "Save", class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium
            ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
            focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4
            [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-200 text-blue-800 hover:bg-blue-300 hover:text-blue-900 cursor-pointer', id: 'save-button' %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>
