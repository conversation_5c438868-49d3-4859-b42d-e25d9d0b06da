<div class="flex-1 rounded-2xl shadow-sm p-3 md:p-5">
    <div>
        <div class="text-sm -mt-4">
            <div class="flex flex-wrap gap-6">
                <div class="w-[calc(50%-12px)] flex flex-col gap-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 flex-1">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-np-autofill-form-type="other"
                            data-np-checked="1" data-np-watching="1">
                            <div class="space-y-4">
                                <%= form.fields_for :docket_info, [OpenStruct.new(form.object.docket_info)] do |ld| %>
                                <div><label class="block text-base font-medium text-gray-800 mb-2">Stump
                                        Shade:</label>
                                        <%= ld.text_field :stump_shade, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500', placeholder: 'Enter stump shade', value: ld.object['stump_shade'] %>
                                </div>
                                <div><label class="block text-base font-medium text-gray-800 mb-2">Vita
                                        Classic:</label>
                                        <%= ld.text_field :vita_classic, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500', placeholder: 'Enter vita classic' %>
                                </div>
                                <div><label class="block text-base font-medium text-gray-800 mb-2">3D
                                        Master:</label>
                                        <%= ld.text_field :three_dimensional_master, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500', placeholder: 'Enter 3D master' %>
                                </div>
                                <div><label class="block text-base font-medium text-gray-800 mb-2">Translucency:</label>
                                    <div class="flex space-x-12 mt-2">
                                        <div class="flex items-center">
                                            <%= ld.check_box :translucency_high, id: 'trans-high', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                                            <label for="trans-high" class="ml-2 text-gray-700">High</label>
                                        </div>
                                        <div class="flex items-center">
                                            <%= ld.check_box :translucency_med, id: 'trans-med', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500', checked: true %>
                                            <label for="trans-med" class="ml-2 text-gray-700">Med</label>
                                        </div>
                                        <div class="flex items-center">
                                            <%= ld.check_box :translucency_low, id: 'trans-low', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                                            <label for="trans-low" class="ml-2 text-gray-700">Low</label>
                                        </div>
                                    </div>
                                </div>
                                <div><label class="block text-base font-medium text-gray-800 mb-2">Surface
                                        Texture:</label>
                                    <div class="flex space-x-12 mt-2">
                                        <div class="flex items-center">
                                            <%= ld.check_box :surface_texture_high, id: 'texture-high', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                                            <label for="texture-high" class="ml-2 text-gray-700">High</label>
                                        </div>
                                        <div class="flex items-center">
                                            <%= ld.check_box :surface_texture_med, id: 'texture-med', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500', checked: true %>
                                            <label for="texture-med" class="ml-2 text-gray-700">Med</label>
                                        </div>
                                        <div class="flex items-center">
                                            <%= ld.check_box :surface_texture_low, id: 'texture-low', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                                            <label for="texture-low" class="ml-2 text-gray-700">Low</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="space-y-2 mt-2">
                                    <div class="flex items-center">
                                        <%= ld.check_box :physcial_impressions_taken, id: 'physical-impressions', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded' %>
                                        <label for="physical-impressions" class="ml-2 text-gray-700">Physical impressions taken</label>
                                    </div>
                                    <div class="flex items-center">
                                        <%= ld.check_box :interoral_scan, id: 'intraoral-scan', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded', checked: true %>
                                        <label for="intraoral-scan" class="ml-2 text-gray-700">Intraoral scan taken</label>
                                    </div>
                                    <div class="flex items-center">
                                        <%= ld.check_box :photos_sent, id: 'clinical-photos', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded' %>
                                        <label for="clinical-photos" class="ml-2 text-gray-700">Clinical photos taken</label>
                                    </div>
                                </div>
                            </div>
                            <div class="relative bg-white rounded-lg border border-gray-200 p-4 flex items-center justify-center">
                                <%= ld.hidden_field :drawnimagedata, class: "tooth-drawing-data" %>
                                <button type="button" class="draw-tooth-btn absolute top-4 right-4 w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200 shadow-sm z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600">
                                        <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                        <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                        <path d="M2 2l7.586 7.586"></path>
                                        <circle cx="11" cy="11" r="2"></circle>
                                    </svg>
                                </button>
                                <div class="w-48 h-72">
                                    <img class="tooth-diagram w-full h-full" src="<% if ld.object.drawnimagedata.blank? %><%= request.base_url %>/images/lds.svg<% else %><%= ld.object.drawnimagedata %><% end %>">
                                </div>
                            </div>
                            
                            <script>
                              document.addEventListener('DOMContentLoaded', function() {
                                console.log('Setting up tooth drawing in design section');
                                
                                const container = document.querySelector('.relative.bg-white.rounded-lg.border.border-gray-200.p-4');
                                const drawButton = container.querySelector('.draw-tooth-btn');
                                const toothImage = container.querySelector('.tooth-diagram');
                                const dataField = container.querySelector('.tooth-drawing-data');
                                
                                console.log('Container:', container);
                                console.log('Draw button:', drawButton);
                                console.log('Tooth image:', toothImage);
                                console.log('Data field:', dataField);
                                
                                if (drawButton && toothImage && dataField) {
                                  drawButton.addEventListener('click', function() {
                                    console.log('Draw button clicked');
                                    
                                    if (typeof window.showMarkerArea === 'function') {
                                      console.log('Opening marker area');
                                      try {
                                        const markerArea = window.showMarkerArea(toothImage);
                                        
                                        // Add event listener to save the image data after drawing
                                        toothImage.addEventListener('load', function() {
                                          console.log('Image loaded, updating data field');
                                          dataField.value = toothImage.src;
                                        });
                                      } catch (error) {
                                        console.error('Error opening marker area:', error);
                                      }
                                    } else {
                                      console.error('showMarkerArea function not found');
                                      alert('Drawing tool is not available');
                                    }
                                  });
                                } else {
                                  console.error('Required elements not found');
                                }
                              });
                            </script>
                        </div>
                        <div class="mt-4"><label class="block text-sm font-medium text-gray-700 mb-2">Further
                                Instructions:</label>
                                <%= ld.text_area :prescription_details, class: 'w-full h-32 border border-gray-200 rounded-lg p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none', placeholder: 'Add any additional instructions here...', value: ld.object.prescription_details %>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="overflow-auto">
                            <div class="flex border-b border-gray-400 pb-4 mb-4">
                                <div class="flex-1 flex justify-between border-r border-gray-400 pr-4">
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :topleight, class: 'teethcheckbox' %> 8
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplseven, class: 'teethcheckbox' %> 7
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplsix, class: 'teethcheckbox' %> 6
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplfive, class: 'teethcheckbox' %> 5
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplfour, class: 'teethcheckbox' %> 4
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplthree, class: 'teethcheckbox' %> 3
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :topltwo, class: 'teethcheckbox' %> 2
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toplone, class: 'teethcheckbox' %> 1
                                    </div>
                                </div>
                                <div class="flex-1 flex justify-between pl-4">
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprone, class: 'teethcheckbox' %> 1
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprtwo, class: 'teethcheckbox' %> 2
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprthree, class: 'teethcheckbox' %> 3
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprfour, class: 'teethcheckbox' %> 4
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprfive, class: 'teethcheckbox' %> 5
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprsix, class: 'teethcheckbox' %> 6
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :toprseven, class: 'teethcheckbox' %> 7
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :topreight, class: 'teethcheckbox' %> 8
                                    </div>
                                </div>
                            </div>
                            <div class="flex">
                                <div class="flex-1 flex justify-between border-r border-gray-400 pr-4">
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomleight, class: 'teethcheckbox' %> 8
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlseven, class: 'teethcheckbox' %> 7
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlsix, class: 'teethcheckbox' %> 6
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlfive, class: 'teethcheckbox' %> 5
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlfour, class: 'teethcheckbox' %> 4
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlthree, class: 'teethcheckbox' %> 3
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomltwo, class: 'teethcheckbox' %> 2
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomlone, class: 'teethcheckbox' %> 1
                                    </div>
                                </div>
                                <div class="flex-1 flex justify-between pl-4">
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrone, class: 'teethcheckbox' %> 1
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrtwo, class: 'teethcheckbox' %> 2
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrthree, class: 'teethcheckbox' %> 3
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrfour, class: 'teethcheckbox' %> 4
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrfive, class: 'teethcheckbox' %> 5
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrsix, class: 'teethcheckbox' %> 6
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomrseven, class: 'teethcheckbox' %> 7
                                    </div>
                                    <div class="text-center text-2xl font-medium px-2">
                                        <%= ld.check_box :bottomreight, class: 'teethcheckbox' %> 8
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <% end %>
                </div>
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 w-[calc(50%-12px)]">
                    <div
                        class="relative bg-white rounded-lg border border-gray-200 p-4 flex items-center justify-center h-full">
                        <button type="button"
                            class="absolute top-4 right-4 text-gray-400 hover:text-blue-600 transition-colors duration-200 bg-white p-2 rounded-full shadow-sm z-10"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-maximize2 w-5 h-5">
                                <polyline points="15 3 21 3 21 9"></polyline>
                                <polyline points="9 21 3 21 3 15"></polyline>
                                <line x1="21" x2="14" y1="3" y2="10"></line>
                                <line x1="3" x2="10" y1="21" y2="14"></line>
                            </svg></button>
                        <%= form.fields_for :docket_info, [OpenStruct.new(form.object.docket_info)] do |ld| %>
                        <%= ld.hidden_field :drawnimagedata_shade %>
                        <div class="w-full max-w-lg">
                            <% if ld.object.drawnimagedata_shade.blank? %>
                            <img id="imagedrawshade" alt="Dental arch top view" class="w-full h-auto"
                                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Teeth%20Top%20View-nKRGpbyPw2sX2ImUYRQ0t9IqYOaUjk.png">
                            <% else %>
                            <img id="imagedrawshade" alt="Dental arch top view" class="w-full h-auto"
                                src="<%= ld.object.drawnimagedata_shade %>">
                            <% end %>
                        </div>
                        <% end %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>