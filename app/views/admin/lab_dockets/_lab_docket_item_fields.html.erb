<div class="nested-fields hidden ldi">
  <div class="ldt-container d-flex flex-column">
    <% if f.object.persisted? %>
      <%= f.hidden_field :id %>
    <% end %>

    <%= f.fields_for :lab_docket_treatments do |builder| %>
      <%= render 'lab_docket_treatment_fields', f: builder %>
    <% end %>

    <div class="d-flex gap-2 ldi-assocs" style="display: none !important;">
      <%= link_to_add_association 'Add treatment', f, :lab_docket_treatments, class: 'btn btn-primary add-treatment btn-sm' %>
      <%= link_to_remove_association "Delete all", f, class: "btn btn-secondary btn-sm" %>
    </div>
  </div>

  <%= f.fields_for :info, [OpenStruct.new(f.object.info)] do |ld| %>
    <div class="flex flex-wrap gap-6">
      <div class="w-[calc(50%-12px)] flex flex-col gap-6">
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 flex-1">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="space-y-4">
              <div>
                <label class="block text-base font-medium text-[14px] text-gray-800 mb-2">Stump Shade:</label>
                <%= ld.text_field :stump_shade, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500', value: ld.object['stump_shade'] %>
              </div>
              <div>
                <label class="block text-base font-medium text-[14px] text-gray-800 mb-2">Vita Classic:</label>
                <%= ld.text_field :vita_classic, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' %>
              </div>
              <div>
                <label class="block text-base font-medium text-[14px] text-gray-800 mb-2">3D Master:</label>
                <%= ld.text_field :three_dimensional_master, class: 'w-full border border-gray-200 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' %>
              </div>
              <div>
                <label class="block text-base font-medium text-[14px] text-gray-800 mb-2">Translucency:</label>
                <div class="flex space-x-12 mt-2">
                  <div class="flex items-center">
                    <%= ld.radio_button :translucency, 'High', id: 'trans-high', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :translucency, 'High', for: 'trans-high', class: 'ml-2 text-gray-700' %>
                  </div>
                  <div class="flex items-center">
                    <%= ld.radio_button :translucency, 'Med', id: 'trans-med', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :translucency, 'Med', for: 'trans-med', class: 'ml-2 text-gray-700' %>
                  </div>
                  <div class="flex items-center">
                    <%= ld.radio_button :translucency, 'Low', id: 'trans-low', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :translucency, 'Low', for: 'trans-low', class: 'ml-2 text-gray-700' %>
                  </div>
                </div>
              </div>
              <div>
                <label class="block text-base font-medium text-[14px] text-gray-800 mb-2">Surface Texture:</label>
                <div class="flex space-x-12 mt-2">
                  <div class="flex items-center">
                    <%= ld.radio_button :surface_texture, 'High', id: 'surface-high', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :surface_texture, 'High', for: 'surface-high', class: 'ml-2 text-gray-700' %>
                  </div>
                  <div class="flex items-center">
                    <%= ld.radio_button :surface_texture, 'Med', id: 'surface-med', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :surface_texture, 'Med', for: 'surface-med', class: 'ml-2 text-gray-700' %>
                  </div>
                  <div class="flex items-center">
                    <%= ld.radio_button :surface_texture, 'Low', id: 'surface-low', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500' %>
                    <%= ld.label :surface_texture, 'Low', for: 'surface-low', class: 'ml-2 text-gray-700' %>
                  </div>
                </div>
              </div>
              <div class="space-y-2 mt-2">
                <div class="flex items-center">
                  <%= ld.check_box :physical_impressions_taken, id: 'physical-impressions', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded' %>
                  <%= ld.label :physical_impressions_taken, 'Physical impressions taken', for: 'physical-impressions', class: 'ml-2 text-gray-700' %>
                </div>
                <div class="flex items-center">
                  <%= ld.check_box :intraoral_scan_taken, id: 'intraoral-scan', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded' %>
                  <%= ld.label :intraoral_scan_taken, 'Intraoral scan taken', for: 'intraoral-scan', class: 'ml-2 text-gray-700' %>
                </div>
                <div class="flex items-center">
                  <%= ld.check_box :clinical_photos_taken, id: 'clinical-photos', class: 'w-5 h-5 text-blue-600 focus:ring-blue-500 rounded' %>
                  <%= ld.label :clinical_photos_taken, 'Clinical photos taken', for: 'clinical-photos', class: 'ml-2 text-gray-700' %>
                </div>
              </div>
            </div>
            <div class="relative bg-white rounded-lg border border-gray-200 p-4 flex items-center justify-center">
              <%= ld.hidden_field :drawnimagedata %>
              <button type="button" id="drawicon1" class="absolute top-4 right-4 text-gray-400 hover:text-blue-600 transition-colors duration-200 bg-white p-2 rounded-full shadow-sm z-10">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                     viewBox="0 0 24 24" fill="none" stroke="currentColor"
                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                     class="lucide lucide-pencil w-5 h-5">
                  <path d="M18 2L22 6L8.5 19.5L3 21L4.5 15.5L18 2Z" />
                </svg>
              </button>

              <img id="imagedraw" height="268" width="185" src="
                  <% if ld.object.drawnimagedata.blank? %><%= request.base_url %>/images/lds.svg
                  <% else %><%= ld.object.drawnimagedata %>
                  <% end %>">
            </div>
          </div>

          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Further Instructions:</label>
            <%= ld.text_area :additional_instructions,
                            placeholder: 'Add any additional instructions here...',
                            class: 'w-full h-32 border border-gray-200 rounded-lg p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none' %>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div class="overflow-auto">
            <div class="flex border-b border-gray-400 pb-4 mb-4">
              <div class="flex-1 flex justify-between border-r border-gray-400 pr-4 pt-2 teethcheckboxlabel">
                <% [8, 7, 6, 5, 4, 3, 2, 1].each do |num| %>
                  <div>
                    <%= ld.label :"topl#{num}", class: "text-center text-2xl font-medium px-2" do %>
                      <%= ld.check_box :"topl#{num}", class: "teethcheckbox" %>
                      <%= num %>
                    <% end %>
                  </div>
                <% end %>
              </div>

              <div class="flex-1 flex justify-between pl-4 pt-2 teethcheckboxlabel">
                <% [1, 2, 3, 4, 5, 6, 7, 8].each do |num| %>
                  <div>
                    <%= ld.label :"topr#{num}", class: "text-center text-2xl font-medium px-2" do %>
                      <%= ld.check_box :"topr#{num}", class: "teethcheckbox" %>
                      <%= num %>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="flex">
              <div class="flex-1 flex justify-between border-r border-gray-400 pr-4 pb-2 teethcheckboxlabel">
                <% [8, 7, 6, 5, 4, 3, 2, 1].each do |num| %>
                  <div>
                    <%= ld.label :"bottoml#{num}", class: "text-center text-2xl font-medium px-2" do %>
                      <%= ld.check_box :"bottoml#{num}", class: "teethcheckbox" %>
                      <%= num %>
                    <% end %>
                  </div>
                <% end %>
              </div>
              <div class="flex-1 flex justify-between pl-4 pb-2 teethcheckboxlabel">
                <% [1, 2, 3, 4, 5, 6, 7, 8].each do |num| %>
                  <div>
                    <%= ld.label :"bottomr#{num}", class: "text-center text-2xl font-medium px-2" do %>
                      <%= ld.check_box :"bottomr#{num}", class: "teethcheckbox" %>
                      <%= num %>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 w-[calc(50%-12px)]">
        <div class="relative bg-white rounded-lg border border-gray-200 p-4 flex items-center justify-center h-full">
          <%= ld.hidden_field :drawnimagedata_shade %>
          <button id="drawicon2" type="button" class="absolute top-4 right-4 text-gray-400 hover:text-blue-600 transition-colors duration-200 bg-white p-2 rounded-full shadow-sm z-10">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                 viewBox="0 0 24 24" fill="none" stroke="currentColor"
                 stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                 class="lucide lucide-pencil w-5 h-5">
              <path d="M18 2L22 6L8.5 19.5L3 21L4.5 15.5L18 2Z" />
            </svg>
          </button>

          <img id="imagedrawshade" height="600" width="414" src="
                    <% if ld.object.drawnimagedata_shade.blank? %><%= request.base_url %>/images/ld rotated.svg
                    <% else %><%= ld.object.drawnimagedata_shade %>
                    <% end %>">
        </div>
      </div>
    </div>

    <div class="mt-6 ai-notes">
      <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <label class="text-lg font-medium text-gray-800 mb-6">Prescription Details</label>
        <div class="relative group">
          <div class="border bg-card text-card-foreground p-0 overflow-hidden border-[#e5e5e5] rounded-xl transition-all duration-200 shadow-sm hover:shadow-md" data-v0-t="card">
            <div class="absolute top-0 left-0 w-full h-1.5 opacity-80 rounded-tl-xl rounded-tr-xl" style="background: linear-gradient(to right, rgb(167, 243, 208), rgb(110, 231, 183));"></div>
            <div class="pt-3 bg-white"></div>
            <%= ld.text_area :prescription_details, rows: 8, class: 'ai-textarea flex w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground
                    focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50
                    min-h-[120px] border-0 shadow-none resize-none px-4 pt-1 pb-10 focus-visible:ring-0 text-sm leading-relaxed',
                             id: 'prescription-editor', value: ld.object.prescription_details
            %>
            <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-white via-white to-transparent ai-input-btns">
              <div class="flex items-center justify-end">
                <div class="flex items-center gap-2">
                  <div class="animation-wave hidden relative flex items-center h-8 px-3 py-1 mr-1 animate-fadeIn overflow-hidden rounded-xl backdrop-blur-md bg-[rgba(239,68,68,0.08)] border border-[rgba(239,68,68,0.2)] shadow-[0_0_12px_rgba(239,68,68,0.15)]">
                    <div class="absolute inset-0 bg-gradient-to-r from-[rgba(239,68,68,0.02)] to-[rgba(239,68,68,0.08)]"></div>
                    <div class="relative flex items-end justify-center h-4 gap-[3px] z-10">
                      <div class="rounded-full" style="width: 2px; height: 35%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 1.5px; height: 60%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 2px; height: 90%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 1.5px; height: 50%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 2px; height: 75%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.15s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 1.5px; height: 100%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.05s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 2px; height: 65%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.25s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 1.5px; height: 85%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 2px; height: 45%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                      <div class="rounded-full" style="width: 1.5px; height: 70%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                    </div>
                    <div class="ml-2 text-[10px] font-medium text-[#ef4444] tracking-wide opacity-80">
                      RECORDING
                    </div>
                  </div>
                  <button <%= 'disabled' if @show.present? %> type="button" class="ai-record inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background
                                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
                                        disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground
                                        rounded-full relative overflow-hidden transition-all duration-300 h-9 w-9 bg-gradient-to-b from-[#fef2f2] to-[#fee2e2] border border-[#fecaca]
                                        shadow-sm text-[#ef4444] hover:shadow-md hover:from-[#fee2e2] hover:to-[#fecaca] active:scale-95 backdrop-blur-sm" aria-label="Start recording" title="Start recording">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic h-4 w-4">
                      <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                      <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                      <line x1="12" x2="12" y1="19" y2="22"></line>
                    </svg>
                  </button>
                  <button <%= 'disabled' if @show.present? %> type="button" data-edit="improve the text" class="ai-pill-button improve-btn inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background
                                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
                                        disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground
                                        h-9 px-3 rounded-full bg-gradient-to-b from-[#fff7ed] to-[#ffedd5] border border-[#fed7aa] shadow-sm text-[#f97316] gap-1.5 transition-all
                                        duration-200 hover:shadow-md hover:from-[#ffedd5] hover:to-[#fed7aa] active:scale-95">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles h-4 w-4">
                      <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                      <path d="M20 3v4"></path>
                      <path d="M22 5h-4"></path>
                      <path d="M4 17v2"></path>
                      <path d="M5 18H3"></path>
                    </svg>
                    <span class="text-xs font-medium">Improve</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  <% end %>
</div>
