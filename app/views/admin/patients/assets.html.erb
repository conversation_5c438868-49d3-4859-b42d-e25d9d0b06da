<div class="min-h-screen bg-gray-50/30">
  <%= render "admin/patients/header", tab: "assets" %>

  <div class="mx-[32px] p-[24px] mb-4">
    <div class="mb-8 flex items-center justify-between">
      <h1 class="text-3xl font-semibold tracking-tight text-gray-900"></h1>
      <div class="flex items-center gap-3">
        <button id="download-assets-btn" type="button"
          class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-blue-200 hover:bg-blue-300 text-blue-800 font-medium px-6 py-2.5 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md text-sm">
            <i class="fa-solid fa-download"></i>
          Download Assets
        </button>
        <button id="upload-asset-btn" type="button"
          class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-orange-200 hover:bg-orange-300 text-orange-800 font-medium px-6 py-2.5 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
          Upload Asset
        </button>
      </div>
    </div>

    <!-- Filters Section -->
    <div id="assets-container" class="" 
         data-assets-url="<%= assets_admin_patient_path(@patient, format: :json) %>" 
         data-update-labels-url="<%= update_asset_labels_admin_patient_path(@patient) %>" 
         data-archive-assets-url="<%= archive_assets_admin_patient_path(@patient) %>">
      <div class="flex justify-between items-center mb-4">
        <div class="flex-1 flex items-center space-x-3">
          <div class="relative flex-1 max-w-md">
            <input placeholder="Search assets..." id="asset-search-input"
              class="w-full px-3 py-2.5 pl-9 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm bg-white/80 backdrop-blur-sm shadow-sm"
              type="text" />
            <svg xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
            
            <button id="all-asset-tab" class="filter-btn active-filter min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden text-indigo-800" style="background: linear-gradient(to right, #c7d2fe, #a5b4fc);" data-filter="all" data-color="indigo">
              <i class="fa-light fa-list h-4 w-4 text-indigo-700"></i>
              <span class="tab-text text-xs font-medium tracking-wide ml-1.5">All</span>
            </button>
            
            <% # Define colors for different asset types
               colors = {
                 "Documents" => "blue",
                 "X-Rays" => "purple",
                 "Clinical Photographs" => "amber",
                 "Invoices" => "orange",
                 "Treatment Plan" => "teal",
                 "Prescription" => "violet",
                 "Medical History" => "emerald",
                 "Consent Forms" => "green",
                 "3D STL Files" => "red",
                 "OPGs" => "indigo",
                 "CBCTs" => "pink",
                 "Lab Dockets" => "cyan"
               }
               
               # Use default colors for any labels not in the map
               default_colors = ["blue", "purple", "amber", "orange", "teal", "green", "red", "indigo", "pink", "cyan"]
            %>
            
            <% @labels.each_with_index do |label, index| %>
              <%
                color = colors[label] || default_colors[index % default_colors.length]
                # Create a safe label for JS by removing apostrophes and spaces
                safe_label = label.gsub("'", "").gsub(" ", "_")
                # Map singular labels to plural for display
                display_label = case label
                                when 'Treatment Plan' then 'Treatment Plans'
                                when 'Prescription' then 'Prescriptions'
                                else label
                                end
              %>
              <button id="<%= safe_label.downcase %>-asset-tab" class="filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="<%= j label %>" data-color="<%= color %>">
                <% # Choose an appropriate icon based on the label type %>
                <% case label 
                   when "Documents" %>
                  <i class="fa-light fa-file-lines h-4 w-4"></i>
                <% when "X-Rays" %>
                  <i class="fa-light fa-x-ray h-4 w-4"></i>
                <% when "Clinical Photographs" %>
                  <i class="fa-light fa-image h-4 w-4"></i>
                <% when "Invoices" %>
                  <i class="fa-light fa-file-invoice h-4 w-4"></i>
                <% when "Treatment Plan" %>
                  <i class="fa-light fa-list-check h-4 w-4"></i>
                <% when "Prescription" %>
                  <i class="fa-light fa-prescription-bottle h-4 w-4"></i>
                <% when "Medical History" %>
                  <i class="fa-light fa-file-medical h-4 w-4"></i>
                <% when "Consent Forms" %>
                  <i class="fa-light fa-signature h-4 w-4"></i>
                <% when "3D STL Files" %>
                  <i class="fa-light fa-cube h-4 w-4"></i>
                <% when "OPGs" %>
                  <i class="fa-light fa-teeth h-4 w-4"></i>
                <% when "CBCTs" %>
                  <i class="fa-light fa-head-side-medical h-4 w-4"></i>
                <% when "Lab Dockets" %>
                  <i class="fa-light fa-flask h-4 w-4"></i>
                <% else %>
                  <i class="fa-light fa-file h-4 w-4"></i>
                <% end %>
                <span class="tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300"><%= display_label %></span>
              </button>
            <% end %>
            <!-- Archived filter toggle -->
            <button id="archived-filter-toggle" 
              class="px-3 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 <%= @show_archived ? 'text-red-800 bg-red-100 hover:bg-red-200' : 'bg-white text-gray-600 hover:bg-gray-50' %>"
              
              data-archived="<%= @show_archived ? 'true' : 'false' %>">
              <i class="fa-light fa-archive h-4 w-4 <%= @show_archived ? 'text-red-700' : '' %>"></i>
            </button>
          </div>
          <div id="asset_toolbar" class="hidden items-center gap-2"><!-- Toolbar for bulk actions -->
            <button id="edit-labels-btn" class="px-3 py-1.5 rounded-lg bg-amber-200 text-amber-800 text-sm hover:bg-amber-300 transition-colors shadow-sm label-assets">
              <i class="fa-light fa-tags h-4 w-4 mr-1"></i>
              Edit Labels
            </button>
            <button id="archive-assets-btn" class="px-3 py-1.5 rounded-lg bg-red-200 text-red-800 text-sm hover:bg-red-300 transition-colors shadow-sm archive-assets">
              <i class="fa-light fa-archive h-4 w-4 mr-1"></i>
              Archive
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- This div is needed for the JavaScript filtering functionality -->
    <div id="assets-grid-container" class="hidden"></div>
    
    <%= admin_table(@assets, assets_table_columns, {
      empty_message: 'No assets found',
      row_class: 'group bg-white shadow-sm rounded-xl hover:shadow-lg transition-all duration-300 ease-in-out border border-gray-200/75 hover:border-gray-300 asset-item',
      row_data: ->(asset) { { "data-asset-id": asset.id, "data-asset-label": asset.label, "data-archived": asset.archived.to_s } }
    }) do |asset| %>
      <%= asset_action_buttons(asset) %>
    <% end %>
  </div>
</div>

<%= render 'admin/patients/asset_upload_modal' %>
<%= render 'admin/patients/image_selection_bar' %>
<%= render 'admin/patients/image_comparison_modal' %>
<%= render 'admin/patients/image_preview_modal' %>
