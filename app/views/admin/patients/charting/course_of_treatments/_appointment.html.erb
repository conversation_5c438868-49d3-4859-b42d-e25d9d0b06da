<%# app/views/admin/patients/charting/course_of_treatments/_appointment.html.erb %>
<% appt_counter ||= appt.position %>

<div class="appointment-block" data-appointment-id="<%= appt.id %>">
  <div class="flex">
    <div class="appointment-toolbar bg-green-50 rounded-lg border border-green-200 shadow-sm overflow-hidden flex items-center mr-3 mb-1.5 p-1.5 w-12 flex-shrink-0">
      <div class="flex flex-col items-center py-2 px-1.5 bg-white rounded-md shadow-sm h-full w-full justify-between">
        <% patient = appt.course_of_treatment.patient %>

        <div class="relative mb-2 group">
          <a href="/admin/patients/<%= patient.id %>/account">
            <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
              <div class="relative">
                <svg width="18" height="18" viewBox="0 0 15 15" fill="black" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.6435 8.05032V7.49769H6.20192C6.16324 7.23795 6.1135 7.00032 6.06377 6.77927C5.95877 6.29295 5.86482 5.83979 5.88692 5.28716C5.93113 4.28137 6.25719 3.59058 6.87061 3.22584C7.79903 2.66216 9.09219 3.07663 9.61719 3.27558L9.53429 3.83374C9.18061 3.68453 7.93719 3.22584 7.15245 3.70111C6.71035 3.96084 6.47271 4.50242 6.43956 5.28716C6.41745 5.78453 6.50587 6.19348 6.60535 6.66874C6.66061 6.91742 6.71587 7.18821 6.76561 7.49769H9.06456V8.05032H6.8264C6.85403 8.26032 6.85403 8.49242 6.85403 8.74111C6.85403 10.4985 6.04166 11.3661 5.38956 11.9187H10.1698V12.4714H4.36719V11.9187L4.81482 11.6811C5.43929 11.1561 6.3014 10.4432 6.3014 8.74111C6.3014 8.49242 6.3014 8.26032 6.27377 8.05032H4.6435Z"
                        fill="<%= account_link_color(patient, appt) %>"/>
                </svg>
              </div>
            </div>
          </a>
        </div>

        <div class="relative mb-2 group">
          <% if appt.lab_works.any? %>
            <% labwork = appt.lab_works.last %>
            <a href="/admin/lab-work/<%= labwork.id %>">
              <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
                <div class="relative">
                  <svg width="18" height="18" aria-hidden="true" data-prefix="fal" data-icon="tooth" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path fill="<%= lab_work_link_color(patient, appt) %>" d="M443.94 96.25c-11.03-45.28-47.12-82.08-92.02-93.73-27.93-7.22-57.56 1.12-89.08 24.34-12.58 9.24-44.2 24.48-77.59.06l-.4-.28c-.06-.04-.13-.04-.19-.08C153.32 3.6 123.92-4.66 96.05 2.54c-44.87 11.64-80.99 48.44-92 93.72-9.61 39.48-1.97 78.7 21.5 110.44 20.51 27.73 32.06 61.84 36.29 107.34 3.62 38.69 9.25 89.61 20.93 140.31l7.81 33.97c3.22 13.95 15.4 23.69 29.65 23.69 14.03 0 26.18-9.55 29.53-23.16l34.5-138.44c4.56-18.33 20.9-31.14 39.74-31.14s35.18 12.81 39.74 31.16l34.5 138.41a30.348 30.348 0 0 0 29.53 23.17c14.25 0 26.43-9.73 29.65-23.69l7.81-33.97c11.69-50.72 17.31-101.64 20.93-140.31 4.28-45.56 15.81-79.66 36.31-107.34 23.47-31.76 31.1-70.98 21.47-110.45zm-47.21 91.41c-24.09 32.56-37.59 71.76-42.43 123.39-3.5 37.7-9 87.23-20.25 136.11l-6.34 27.59-32.9-132.06c-8.12-32.64-37.25-55.42-70.8-55.42s-62.68 22.78-70.8 55.41l-31.43 138.45-7.81-33.97c-11.25-48.86-16.75-98.39-20.25-136.09-4.81-51.56-18.31-90.78-42.45-123.41-17.72-23.97-23.45-53.75-16.12-83.84 8.26-33.98 35.32-61.58 68.94-70.3 24.3-6.27 48.55 9.13 62.12 19.14.07.05.15.08.21.13.03.02.04.05.07.07l80.33 56.25c10.7 7.43 20.15-.87 22.28-3.94 5.09-7.23 3.31-17.2-3.91-22.28L245.5 69.11c13.05-3.13 25.54-8.55 36.31-16.47 23.5-17.36 43.74-23.89 62.09-19.14 33.62 8.73 60.71 36.33 68.99 70.31 7.3 30.1 1.59 59.88-16.16 83.85z" class=""></path>
                  </svg>
                </div>
              </div>
            </a>
          <% else %>
            <a href="/admin/patients/<%= patient.id %>/labwork">
              <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
                <div class="relative">
                  <svg width="18" height="18" aria-hidden="true" data-prefix="fal" data-icon="tooth" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path fill="gray" d="M443.94 96.25c-11.03-45.28-47.12-82.08-92.02-93.73-27.93-7.22-57.56 1.12-89.08 24.34-12.58 9.24-44.2 24.48-77.59.06l-.4-.28c-.06-.04-.13-.04-.19-.08C153.32 3.6 123.92-4.66 96.05 2.54c-44.87 11.64-80.99 48.44-92 93.72-9.61 39.48-1.97 78.7 21.5 110.44 20.51 27.73 32.06 61.84 36.29 107.34 3.62 38.69 9.25 89.61 20.93 140.31l7.81 33.97c3.22 13.95 15.4 23.69 29.65 23.69 14.03 0 26.18-9.55 29.53-23.16l34.5-138.44c4.56-18.33 20.9-31.14 39.74-31.14s35.18 12.81 39.74 31.16l34.5 138.41a30.348 30.348 0 0 0 29.53 23.17c14.25 0 26.43-9.73 29.65-23.69l7.81-33.97c11.69-50.72 17.31-101.64 20.93-140.31 4.28-45.56 15.81-79.66 36.31-107.34 23.47-31.76 31.1-70.98 21.47-110.45zm-47.21 91.41c-24.09 32.56-37.59 71.76-42.43 123.39-3.5 37.7-9 87.23-20.25 136.11l-6.34 27.59-32.9-132.06c-8.12-32.64-37.25-55.42-70.8-55.42s-62.68 22.78-70.8 55.41l-31.43 138.45-7.81-33.97c-11.25-48.86-16.75-98.39-20.25-136.09-4.81-51.56-18.31-90.78-42.45-123.41-17.72-23.97-23.45-53.75-16.12-83.84 8.26-33.98 35.32-61.58 68.94-70.3 24.3-6.27 48.55 9.13 62.12 19.14.07.05.15.08.21.13.03.02.04.05.07.07l80.33 56.25c10.7 7.43 20.15-.87 22.28-3.94 5.09-7.23 3.31-17.2-3.91-22.28L245.5 69.11c13.05-3.13 25.54-8.55 36.31-16.47 23.5-17.36 43.74-23.89 62.09-19.14 33.62 8.73 60.71 36.33 68.99 70.31 7.3 30.1 1.59 59.88-16.16 83.85z" class=""></path>
                  </svg>
                </div>
              </div>
            </a>
          <% end %>
        </div>

        <div class="relative mb-2 group" data-bs-toggle="modal" data-bs-target="#signatureDocumentsModal-<%= appt.id %>">
          <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
            <div class="relative">
              <svg width="18" height="18" viewBox="0 0 22 22" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.8653 3.40673C14.5485 2.73968 15.467 2.36876 16.4218 2.37439C17.3766 2.38002 18.2907 2.76174 18.966 3.43679C19.6412 4.11185 20.0232 5.02584 20.0291 5.98064C20.035 6.93543 19.6644 7.85408 18.9975 8.53743L18.5938 8.93963L18.7215 9.06727C19.2779 9.62391 19.5905 10.3788 19.5905 11.1659C19.5905 11.9529 19.2779 12.7078 18.7215 13.2644L16.5843 15.4016C16.4443 15.5368 16.2569 15.6116 16.0623 15.6099C15.8677 15.6082 15.6816 15.5302 15.544 15.3926C15.4065 15.255 15.3284 15.0689 15.3267 14.8743C15.325 14.6797 15.3998 14.4923 15.535 14.3523L17.6722 12.2151C17.9504 11.9368 18.1067 11.5594 18.1067 11.1659C18.1067 10.7723 17.9504 10.3949 17.6722 10.1166L17.5445 9.98893L9.16504 18.3699C9.06955 18.4649 8.95001 18.5321 8.81923 18.5643L2.88264 20.0485C2.75372 20.0805 2.61858 20.0773 2.4913 20.0393C2.36403 20.0013 2.2493 19.9298 2.15908 19.8323C2.06887 19.7348 2.00647 19.6149 1.97841 19.485C1.95036 19.3552 1.95766 19.2202 1.99957 19.0942L3.85475 13.5286C3.89094 13.4197 3.9519 13.3206 4.03285 13.2392L13.8653 3.40673ZM17.9467 4.45602C17.5446 4.05453 16.9996 3.82903 16.4314 3.82903C15.8632 3.82903 15.3182 4.05453 14.9161 4.45602L5.20681 14.1638L3.83398 18.2809L8.25971 17.1752L17.9482 7.48814C18.3502 7.08598 18.576 6.54067 18.576 5.97208C18.576 5.4035 18.3502 4.85818 17.9482 4.45602M7.8486 23.039C6.01567 23.039 4.5716 22.199 3.61135 21.3961L5.517 20.9212C6.22613 21.3296 7.02878 21.5479 7.84711 21.5549C8.44968 21.5549 9.07005 21.3115 9.73198 20.8766C10.3939 20.4418 11.0351 19.857 11.6822 19.2515L11.8499 19.0927C12.4213 18.5569 13.0134 17.9989 13.5804 17.6145C14.1859 17.2019 14.9903 16.8279 15.8318 17.1529C16.3899 17.3666 16.752 17.7614 17.0058 18.2452C17.2447 18.6994 17.4095 19.2663 17.5698 19.8852C17.644 20.1672 17.7672 20.4403 17.9111 20.6243C17.9582 20.6885 18.0154 20.7446 18.0803 20.7906C18.1174 20.8128 18.1337 20.8128 18.1382 20.8128C18.3193 20.8128 18.6294 20.6911 19.0732 20.3884C19.3478 20.2014 19.5838 20.0143 19.8183 19.8259C19.9627 19.7101 20.1121 19.5938 20.2665 19.4771C20.6805 19.1669 21.0382 18.9591 21.3009 18.827C21.4352 18.7587 21.573 18.6973 21.7135 18.643L21.7432 18.6311L21.7536 18.6282L21.758 18.6267H21.7595L21.997 19.3287L21.761 18.6252C21.9467 18.5648 22.1486 18.58 22.3232 18.6675C22.4977 18.755 22.6307 18.9077 22.6934 19.0926C22.7561 19.2775 22.7434 19.4797 22.658 19.6553C22.5726 19.8309 22.4215 19.9657 22.2374 20.0307L22.2315 20.0336L22.1855 20.0515C22.14 20.0693 22.0687 20.1024 21.9718 20.1509C21.7788 20.2474 21.4953 20.4106 21.1555 20.6659C21.0679 20.7297 20.9551 20.8202 20.8245 20.9241C20.5559 21.1364 20.2175 21.405 19.9073 21.6157C19.4472 21.9304 18.8075 22.2985 18.1382 22.2985C17.4911 22.2985 17.0236 21.9052 16.7342 21.5311C16.4511 21.1509 16.2467 20.718 16.1331 20.2577C15.9699 19.63 15.8422 19.2188 15.6938 18.9369C15.5602 18.6875 15.4415 18.5925 15.299 18.5361C15.1773 18.4916 14.9354 18.4872 14.416 18.8419C13.9514 19.1565 13.4438 19.6329 12.8457 20.1954L12.6973 20.3334C12.0576 20.9345 11.3319 21.6024 10.5468 22.1174C9.76018 22.6339 8.85485 23.039 7.8486 23.039Z"
                      fill="gray"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="relative mb-2 group">
          <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
            <div class="relative">
              <svg width="18" height="18" viewBox="0 0 22 22" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.47938 22.1489C2.6057 22.1489 1.91208 21.8152 1.39851 21.1478C0.884065 20.4803 0.731476 19.7412 0.940741 18.9305L3.42183 9.76041C2.71905 9.52449 2.11655 9.07409 1.61431 8.40922C1.11208 7.74435 0.860959 6.97867 0.860959 6.11219C0.860959 5.05097 1.24505 4.14202 2.01322 3.38536C2.7814 2.62869 3.70521 2.25078 4.78467 2.25164H17.3536L13.4299 9.97274H8.77639L8.29116 11.7795H9.41334V15.64H7.24353L6.01017 20.283C5.85061 20.8338 5.53759 21.2824 5.0711 21.629C4.60461 21.9756 4.07491 22.1489 3.47938 22.1489ZM3.47546 20.8621C3.78064 20.8621 4.04222 20.7763 4.2602 20.6047C4.47818 20.4331 4.63077 20.2079 4.71797 19.9291L7.4214 9.97274H4.70619L2.20025 19.2535C2.09126 19.661 2.16755 20.0308 2.42913 20.3628C2.69072 20.6948 3.03949 20.8612 3.47546 20.8621ZM19.1454 3.09324L18.6314 1.97368L22.7186 0.148926L23.2261 1.27492L19.1454 3.09324ZM22.7186 12.0755L18.6314 10.2829L19.1468 9.15688L23.2261 10.9829L22.7186 12.0755ZM19.3233 6.73117V5.49322H23.85V6.73117H19.3233ZM4.78467 8.68589H12.6321L15.2479 3.53849H4.78467C4.06532 3.53849 3.44974 3.79071 2.93791 4.29516C2.42608 4.7996 2.16973 5.40528 2.16886 6.11219C2.16799 6.8191 2.42434 7.42521 2.93791 7.93051C3.45148 8.43582 4.06707 8.68761 4.78467 8.68589Z"
                      fill="gray"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="relative mb-2 group">
          <div class="p-1.5 bg-neutral-200 rounded-full group-hover:bg-neutral-300 transition-all duration-200">
            <div class="relative">
              <i class="fa-sharp fa-thin fa-file-medical"
                 style="font-size:18px; color:<%= medical_history_link_color(patient, appt) %>"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- ──────────────────── APPOINTMENT CARD ──────────────────── -->
    <div class="appointment-container text-card-foreground border border-green-200 bg-green-50 shadow-sm rounded-lg overflow-visible mb-1.5 flex-1 flex flex-col min-h-[300px]"
         style="flex-grow:1"
         data-appointment-id="<%= appt.id %>"
         data-locked="<%= appt.locked %>"
         <% if appt.locked_at %>data-locked-at="<%= appt.locked_at.iso8601 %>"<% end %>
         data-appt-dentist-id="<%= appt.dentist_id %>"
         data-appt-dentist-name="<%= appt.dentist.full_name if appt.dentist_id.present? %>">

      <!-- HEADER STRIP (index / dentist / menu) -->
      <div class="flex px-2 pt-2 pb-1.5">
        <!-- left (index + lock) -->
        <div class="flex items-center gap-1 mr-2">
          <span class="apptid text-3xl font-bold text-green-700"><%= appt_counter + 1 %></span>
          <% if appt.locked %>
            <div class="flex flex-col items-center">
              <%
                time_since_locked = appt.locked_at ? Time.current - appt.locked_at : 0
                can_unlock = appt.locked_at && time_since_locked < 24.hours
              %>
              <span class="material-symbols-outlined text-[#8999B0] text-[22px] <%= 'cursor-pointer' if can_unlock %>"
                    <% if can_unlock %>data-appointment-id="<%= appt.id %>"<% end %>>
                lock
              </span>
              <% if appt.locked_at %>
                <% time_left = 24.hours - time_since_locked %>
                <% if time_left > 0 %>
                  <div class="countdown-timer text-[12px] text-[#8999B0] mt-1"
                       data-appointment-id="<%= appt.id %>"
                       data-locked-at="<%= appt.locked_at.iso8601 %>">
                    <%= Time.at(time_left).utc.strftime('%H:%M:%S') %>
                  </div>
                <% end %>
              <% else %>
                <span class="text-[12px] text-[#8999B0] mt-1">Locked</span>
              <% end %>
            </div>
          <% else %>
            <span class="material-symbols-outlined lock-icon text-[#8999B0] text-[22px] cursor-pointer"
                  data-appointment-id="<%= appt.id %>">
              lock_open
            </span>
          <% end %>
        </div>

        <!-- right (dentist / booking pill / menu) -->
        <div class="flex-1 ms-auto">
          <div class="appointment-row flex items-center justify-between bg-white rounded-md p-2 border border-green-100 shadow-sm flex-1"
               data-appointment-id="<%= appt.id %>"
               data-id="<%= appt_counter %>"
               data-appt-dentist-id="<%= appt.dentist_id %>"
               data-appt-dentist-name="<%= appt.dentist.full_name if appt.dentist_id.present? %>">

            <%= render 'layouts/shared/user_avatar', user: appt.dentist, width: 24, height: 24 %>
            <span class="appt-employee-name cursor-pointer inline-flex items-center rounded-full px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-blue-50 text-blue-600 border-0 hover:bg-blue-50 text-xs">
              <%= appt.dentist.full_name if appt.dentist_id.present? %>
            </span>

            <!-- pill + dropdown menu -->
            <div class="ml-auto flex items-center">
              <% if appt.calendar_booking.present? %>
                <!-- Calendar booking is associated -->
                <button type="button" class="bg-green-50 text-green-600 border-0 flex items-center gap-1 py-0.5 px-2 rounded-full text-xs font-medium"
                        data-appointment-id="<%= appt.id %>"
                        onclick="openCalendarBookingModal(<%= appt.id %>)">
                  <span class="material-symbols-outlined mb-[3px]">calendar_month</span>
                  <span><%= appt.calendar_booking.start_time.strftime('%d %b %Y at %I:%M %p') rescue 'Invalid Date' %></span>
                </button>
              <% else %>
                <!-- No calendar booking associated -->
                <button type="button"
                        class="bg-blue-50 text-blue-600 border-0 hover:bg-blue-100 flex items-center gap-1 py-0.5 px-2 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors cursor-pointer"
                        data-appointment-id="<%= appt.id %>"
                        onclick="openCalendarBookingModal(<%= appt.id %>)">
                  <span class="material-symbols-outlined mb-[3px]">calendar_month</span>
                  <span>Not yet booked</span>
                </button>
              <% end %>
            </div>

            <div class="relative circledropdown">
              <!-- trigger ------------------------------------------------------------- -->
              <button
                id="circledropdownbtn-<%= appt_counter %>"
                class="js-dropdown-toggle inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 rounded-full text-neutral-500 hover:text-neutral-700 hover:bg-green-100"
                type="button"
                aria-expanded="false">
                <span class="material-symbols-outlined">more_horiz</span>
              </button>

              <!-- menu ---------------------------------------------------------------- -->
              <div
                id="circledropdown-menu-<%= appt_counter %>"
                class="js-dropdown-menu hidden absolute shadow-lg z-50 mt-2 w-56 rounded-lg bg-gray-200 shadow divide-y divide-gray-100 text-xs">
                <ul class="py-2 text-sm text-gray-700" aria-labelledby="circledropdownbtn-<%= appt_counter %>">
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload Xray</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload CBCT</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload OPG</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload photo</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li>
                    <a class="dropdown-item change-course-of-treatment block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Change Course of Treatment</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li>
                    <a class="dropdown-item delete-appointment block px-4 py-2 hover:bg-gray-100 text-red-600 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Delete</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li class="change-dentist-item">
                    <a class="dropdown-item change-dentist block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Change Dentist</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="accordion sortableAppointments sortable px-2"
           data-appointment-id="<%= appt.id %>">
        <%= render partial: 'admin/patients/charting/course_of_treatments/charted_treatment',
                   collection: appt.charted_treatments.sort_by(&:position_order),
                   as: :charted_treatment,
                   locals: { appt: appt } %>
      </div>

      <!-- SUMMARY BAR -->
      <div class="flex justify-end justify-center px-2 pt-1 pb-1.5">
        <div class="bg-white rounded-md shadow-sm border border-green-100 mb-1 overflow-hidden w-1/3">
          <div class="grid grid-cols-[auto_1fr_80px_80px_80px] gap-2 items-center p-1.5">
            <div class="h-5 w-5 flex items-center justify-center rounded-full bg-neutral-50 border border-neutral-200 text-neutral-500">
              <span class="text-xs font-medium">Σ</span>
            </div>

            <div class="ml-2">
              <div class="text-xs font-medium text-neutral-800">
                Total
              </div>
            </div>

            <div class="flex items-center justify-center">
              <div class="flex items-center gap-1 text-xs text-neutral-600">
                <span class="material-symbols-outlined">
                  timer
                </span>

                <div class="appointment-total-duration-time cursor-pointer"
                     onclick="$('.appt-duration').hide();$('.appt-duration').prev().show();$(this).next().show();$(this).hide();$(this).next().focus().select();">
                  0h&nbsp;00m
                </div>

                <input type="number"
                       class="form-control appt-duration"
                       step="0.01"
                       data-id="<%= appt.id %>"
                       style="font-size:14px; display:none; max-width:75px;"
                       onblur="updateAppointmentTotalDuration(this);"
                       onkeydown="if(event.key === 'Enter') { updateAppointmentTotalDuration(this); }"/>
              </div>
            </div>

            <div class="appointment-total-price text-xs font-medium text-neutral-800 text-center">
              £0.00
            </div>

            <div class="flex items-center justify-center gap-1"></div>
          </div>
        </div>
      </div>

      <!-- APPOINTMENT NOTES LIST -->
      <div class="appointment-notes-list pt-1.5 border-t border-green-100 px-2 pt-1 pb-1.5 mt-auto"
           data-appointment-id="<%= appt.id %>"
           id="notes-section-<%= appt.id %>">
        <div class="flex items-center">
          <div class="text-xs font-medium text-neutral-700">Appointment Notes</div>
          <button class="edit-note inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border-input hover:text-accent-foreground ml-2 h-5 px-2 text-xs bg-green-500 text-white hover:bg-green-600 rounded-full border-0 shadow-sm"
                  data-note-toggle="#note-collapse-<%= appt.id %>"
                  data-appointment-id="<%= appt.id %>">
            <span class="material-symbols-outlined">
              add
            </span>
            <span>Add Note</span>
          </button>
        </div>

        <% appt.appointment_notes.select(&:persisted?).each do |note| %>
          <%= render 'admin/patients/charting/course_of_treatments/appointment_note',
                     note: note, source: 'appt', appt: appt %>
        <% end %>
      </div>

      <!-- FOOTER: add / edit note -->
      <div class="appointment-footer flex flex-wrap">
        <div class="flex gap-2 ms-2 w-full items-center justify-between">
          <!-- collapse toggle -->
          <!-- hidden buttons -->
          <button class="btn appt-note-template hidden"
                  style="height:35px; border:1px solid #2A4558; margin-left:auto; margin-right:10px; background:linear-gradient(90deg,#BDCFDB 0%,#BDCFDB 100%);"
                  data-bs-target="#templateNotesModal"
                  data-bs-toggle="modal"
                  data-appt-id="<%= appt.id %>"
                  data-cot-id="<%= appt.course_of_treatment.id %>"
                  data-type="appointment">
            Import Note Template
          </button>
          <a class="btn yellowbtn hidden"
             href="charting/ai_note/appointment/<%= appt.id %>"
             style="height:35px; border:1px solid #2A4558; margin-top:2px;">
            Generate AI Clinical Note
          </a>
        </div>

        <!-- COLLAPSIBLE NOTE FORM -->
        <div class="flex flex-col gap-2 ms-2 w-full">
          <div id="noteCollapse<%= appt.id %>"
               class="accordion-item">
            <div id="note-collapse-<%= appt.id %>" class="hidden accordion-body mt-[10px]">
              <%= form_with model: appt.appointment_notes.new,
                            url: create_charting_appointment_note_admin_patient_charting_path(appt),
                            id: 'add-appointment-note-form' do |f| %>
                <%= f.hidden_field :charting_appointment_id, value: appt.id %>

                <div class="border-t border-neutral-100">


                  <div class="flex bg-green-50/50">
                    <div class="w-1/2 p-4 border-r border-green-100">
                      <div class="space-y-4">
                        <div class="flex items-center justify-between">
                          <%= f.label :completed_at, 'Completed on', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.date_field :completed_at,
                                             value: Date.today.strftime('%Y-%m-%d'),
                                             class: 'appointment_note_completed_at_input flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200' %>
                          </div>
                        </div>

                        <div class="flex items-center justify-between">
                          <%= f.label :practitioner_id, 'Practitioner', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.select :practitioner_id,
                                         options_from_collection_for_select(
                                           f.object.practitioner ? [f.object.practitioner] : [current_user],
                                           :id, :full_name, f.object.practitioner_id || current_user.id),
                                         {},
                                         id: "practitioner-#{appt.id}",
                                         class: "practitioner-#{appt.id} note-practitioner-select flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200",
                                         data: { placeholder: 'Select practitioner' } %>
                          </div>
                        </div>

                        <div class="flex items-center justify-between">
                          <%= f.label :nurse_id, 'Nurse', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.select :nurse_id,
                                         options_from_collection_for_select(
                                           f.object.nurse ? [f.object.nurse] : [],
                                           :id, :full_name, f.object.nurse_id),
                                         {},
                                         id: "nurse-#{appt.id}",
                                         class: "nurse-#{appt.id} note-practitioner-select flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200",
                                         data: { placeholder: 'Select nurse' } %>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="ai-notes w-1/2 p-4">
                      <div class="mic-div flex flex-col h-full">
                        <div style="height:10px"></div>

                        <div class="relative ai-notes">
                          <%= f.text_area :content,
                                          id: "notes_#{appt.id}",
                                          class: "form-control notes-textarea notes-textarea tinymce_editor ai-textarea ai-textarea",
                                          rows: 10,
                                          placeholder: 'Notes',
                                          style: 'height:470px;margin:0px 10px;' %>
                          <%= render 'admin/treatment_plan_options/option/ai_tools', unique_id: 'reason-ai' %>

                          <!-- (spinner overlay left intact) -->
                          <div class="spinner-overlay" style="display:none;">…</div>
                        </div>

                        <!-- action buttons -->
                        <div class="flex items-center justify-end" style="margin:0 11px 11px 11px;background-color:rgb(255,255,255);width:97%;border-radius:0px 0px 14px 14px;">
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- SAVE BUTTON STRIP -->
                  <div class="flex items-center justify-between p-3 bg-white border-t border-neutral-100 justify-end">
                    <div class="flex items-center gap-2">
                      <button type="button"
                              class="cancel-app-note px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors flex items-center gap-1"
                              data-note-toggle="#note-collapse-<%= appt.id %>"
                              data-appointment-id="<%= appt.id %>">
                        Cancel
                      </button>
                      <%= f.submit 'Save',
                                   class: 'save-app-note px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors flex items-center gap-1',
                                   data: { disable_with: nil, 'note-toggle' => "#note-collapse-#{appt.id}", 'appointment-id' => appt.id } %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="newappt"
       class="flex items-center my-4 group"
       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>"
       data-patient-id="<%= patient.id %>"
       data-current-position="<%= appt.position %>">

    <div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div>

    <button class="mx-3 px-4 py-1 text-xs font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-full border border-green-200 shadow-sm transition-all duration-200 hover:shadow flex items-center group-hover:scale-105">
      <span class="material-symbols-outlined calendar-add-on">calendar_add_on</span>
      <span>Add Appointment</span>
    </button>

    <div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div>
  </div>
</div>
