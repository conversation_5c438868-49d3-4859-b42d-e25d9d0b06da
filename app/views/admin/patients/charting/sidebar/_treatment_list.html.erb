<div id="treatment_list" class="content-section hidden space-y-5">
  <!-- ─── top toggle buttons ────────────────────────────────────────────── -->
  <div class="bg-gray-100 p-1 rounded-xl shadow-inner">
    <div class="relative flex">
      <div class="btn-group w-full flex" role="group">
        <button type="button"
                class="active flex-1 py-1.5 text-sm font-medium z-10 relative transition-colors duration-200 focus:outline-none text-gray-800"
                id="btn-treatments">
          Treatments
        </button>
        <button type="button"
                class="flex-1 py-1.5 text-sm font-medium z-10 relative transition-colors duration-200 focus:outline-none text-gray-500"
                id="btn-templates">
          Templates
        </button>
        <div class="absolute top-0 h-full w-1/2 bg-white rounded-lg shadow-sm transition-transform duration-200 ease-spring translate-x-0" id="tab-slider"></div>
      </div>
    </div>
  </div>

  <div id="treatment-templates" class="hidden space-y-5">
    <div id="template-list" class="space-y-4">
      <% CotTemplate.all.each do |template| %>
        <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:shadow-sm transition-shadow">
          <span class="text-sm font-medium text-gray-700"><%= template.name %></span>
          <button class="template-plus-button flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  data-appointments="<%= template.appointments.to_json %>">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
          </button>
        </div>
      <% end %>
    </div>

    <div id="template-form" class="hidden space-y-4 overflow-y-scroll h-[60vh]"></div>
  </div>

  <!-- ─── payment-plan selector & search ─────────────────────────────────── -->
  <div id="treatment-types-container" class="space-y-5">
    <div class="dropdown-course-of-treatment-container">
      <h3 class="text-sm font-medium mb-2 text-gray-500 px-1">Payment Plans</h3>

      <select id="treatment-types-dropdown-select"
              class="form-select w-full px-3 py-2 text-sm border rounded-md border-gray-300 bg-white text-gray-700 focus:ring-2 focus:ring-indigo-200 focus:border-blue-200"
              onchange="selectTreatmentPlan(this.value)">
        <% @cot_payment_plans.each do |plan| %>
          <option value="<%= plan.name %>" <%= 'selected' if @patient.active_cot_payment_plan&.name&.downcase == plan.name.downcase %>>
            <%= plan.name %>
          </option>
        <% end %>
      </select>
    </div>

    <div>
      <div class="relative">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-gray-400"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>

        <input type="text"
               id="treatment-search"
               placeholder="Search treatments..."
               class="flex w-full border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-9 rounded-full border-gray-300 bg-white h-9 text-sm"/>
      </div>
    </div>

    <% if @favorite_treatments.any? %>
      <div class="favorite-treatments space-y-5">
        <h2 class="text-sm flex items-center font-medium mb-2 text-gray-800 px-1">
          <span class="material-symbols-outlined text-sm text-yellow-500 mr-1">star</span>
          Favorite Treatments
        </h2>

        <div class="treatment-list space-y-1">
          <% @favorite_treatments.each do |t| %>
            <% next unless @current_practice == t.practice %>

            <div class="treatment-container flex items-center gap-2 p-1.5 rounded-lg transition-all duration-200 bg-white border cursor-pointer border-gray-100 shadow-sm hover:bg-gray-50 hover:shadow">
              <button class="favorite-btn flex h-7 w-7 items-center justify-center rounded-full border border-yellow-300 text-yellow-500 bg-yellow-50 hover:bg-yellow-100"
                   data-treatment-full   ="<%= t.is_full_tooth? %>"
                   data-treatment-bridge ="<%= t.bridge_treatment %>"
                   data-treatment-id     ="<%= t.id %>"
                   data-favorited        ="true"
                   data-category-name    ="Favorites">
                <span class="material-symbols-outlined text-sm font-variation-settings-filled">star</span>
              </button>
              <span class="treatment-code text-xs">
                <%= t.code %>
              </span>
              <span class="text-xs">
                -
              </span>
              <span class="treatment text-sm"
                    data-treatment-full   ="<%= t.is_full_tooth? %>"
                    data-treatment-id     ="<%= t.id %>"
                    data-treatment-bridge ="<%= t.bridge_treatment %>"
                    data-treatment-code   ="<%= t.code %>"
                    data-treatment-folder ="<%= t.treatment_folder %>"
                    data-region           ="<%= t.region %>"
                    data-missing-tooth    ="<%= t.missing_tooth %>"
                    data-category-name    ="Favorites">
                <%= t.patient_friendly_name %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <% @treatment_categories.each do |tc| %>
      <div class="treatment-category space-y-5">
        <h2 class="text-sm font-medium mb-2 text-gray-800 px-1"><%= tc.name %></h2>

        <div class="treatment-list space-y-1">
          <% tc.treatments.each do |t| %>
            <% next unless @current_practice == t.practice %>
            <% next if @favorite_treatments.include?(t) %>

            <div class="treatment-container flex items-center gap-2 p-1.5 rounded-lg transition-all duration-200 bg-white border cursor-pointer border-gray-100 shadow-sm hover:bg-gray-50 hover:shadow">
              <button class="favorite-btn flex h-7 w-7 items-center justify-center rounded-full border border-gray-300 text-gray-500 hover:bg-white"
                   data-treatment-full   ="<%= t.is_full_tooth? %>"
                   data-treatment-bridge ="<%= t.bridge_treatment %>"
                   data-treatment-id     ="<%= t.id %>"
                   data-favorited        ="false"
                   data-category-name    ="<%= tc.name %>">
                <span class="material-symbols-outlined text-sm">star</span>
              </button>
              <span class="treatment-code text-xs">
                <%= t.code %>
              </span>
              <span class="text-xs">
                -
              </span>
              <span class="treatment text-sm"
                    data-treatment-full   ="<%= t.is_full_tooth? %>"
                    data-treatment-id     ="<%= t.id %>"
                    data-treatment-bridge ="<%= t.bridge_treatment %>"
                    data-treatment-code   ="<%= t.code %>"
                    data-treatment-folder ="<%= t.treatment_folder %>"
                    data-region           ="<%= t.region %>"
                    data-missing-tooth    ="<%= t.missing_tooth %>"
                    data-category-name    ="<%= tc.name %>">
                <%= t.patient_friendly_name %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
