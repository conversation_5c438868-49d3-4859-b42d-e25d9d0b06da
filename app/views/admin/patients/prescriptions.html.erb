<%= render "admin/patients/header", tab: "actions" %>

<div class="min-h-screen bg-gray-50/30 p-4">
  <div class="mx-auto">
    <div class="mb-8 flex items-center justify-between">
      <h1 class="text-3xl font-semibold tracking-tight text-gray-900"></h1>
      <a href="<%= new_admin_prescription_path(patient_id: @patient.id) %>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-orange-200 hover:bg-orange-300 text-orange-800 font-medium px-6 py-2.5 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md text-sm">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
          <path d="M5 12h14"></path>
          <path d="M12 5v14"></path>
        </svg>
        Create Prescription
      </a>
    </div>
    
    <%= admin_table(@prescriptions, prescription_table_columns, { empty_message: 'No prescriptions found' }) do |prescription| %>
      <div class="flex items-center justify-end gap-3 group">
        <% if !prescription.signed? %>
          <%= link_to edit_admin_prescription_path(prescription), class: 'inline-flex items-center gap-1 text-xs font-medium text-blue-600 hover:text-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-200' do %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pen-line h-3 w-3">
              <path d="M12 20h9"></path>
              <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
            </svg>
            Edit
          <% end %>
        <% else %>
          <%= link_to email_admin_prescription_path(prescription), class: 'inline-flex items-center gap-1 text-xs font-medium text-blue-600 hover:text-blue-800 opacity-0 group-hover:opacity-100 transition-opacity duration-200' do %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-3 w-3">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            Email
          <% end %>
        <% end %>
        <%= link_to admin_prescription_path(prescription), class: 'inline-flex items-center gap-1 text-xs font-medium text-gray-600 hover:text-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-200' do %>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye h-3 w-3">
            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          View
        <% end %>
      </div>
    <% end %>
  </div>
</div>
