<div
  class="fixed z-1000 w-[800px] transition-all duration-300 ease-in-out flex right-0 border-l border-gray-200 bg-white shadow-lg" style="transform: translateX(100%); top: 56px; bottom: 0;" id="actions-sidebar" data-default-avatar-url="<%= asset_path('default-avatar.webp') %>">
  <div class="flex-1 flex flex-col">
    <div class="p-4 flex items-center justify-between border-b border-gray-200 bg-gradient-to-b from-white to-gray-50">
      <div class="flex items-center gap-3"><button
          class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 py-2 rounded-full h-10 px-5 bg-gradient-to-r from-sky-100 to-sky-200 hover:from-sky-200 hover:to-sky-300 text-gray-700 shadow-md transition-all hover:shadow-lg" data-modal-target="sidebarNewActionModal"><svg
            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-plus h-4 w-4 mr-2">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>New Action</button>
        <div class="h-6 w-px bg-gray-200"></div>
        <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm filter-container tab-filter-container">
                  <%= apple_filter_button("All Actions",
                icon: "fa-light fa-list",
                color: "purple",
                active: true,
                id: "all-tab",
                data: { tab: "all", group: "tabs" },
                class: "tab") %>
          <%= apple_filter_button("Today",
                icon: "fa-light fa-calendar-day",
                color: "blue",
                active: false,
                id: "today-tab",
                data: { tab: "today", group: "tabs" },
                class: "tab") %>

        </div>
        <div class="h-6 w-px bg-gray-200 mx-1"></div>
        <%= apple_filter_button("Completed",
              icon: "fa-light fa-circle-check",
              color: "emerald",
              active: false,
              id: "completed-tab",
              data: { tab: "completed", group: "tabs" },
              class: "tab") %>
      </div><button
        id="close-actions"
        class="gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:text-accent-foreground rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100"><svg
          xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="lucide lucide-x h-5 w-5 text-gray-500">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg></button>
    </div>
    <div class="flex-1 overflow-auto">
      <div class="sticky top-0 z-10 bg-white/90 backdrop-blur-sm">
        <div class="flex items-center justify-between px-4 pt-4 pb-3">
          <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm filter-container action-type-filter-container">
            <%= apple_filter_button("All",
                  icon: "fa-light fa-list",
                  color: "blue",
                  active: true,
                  data: { filter: "all", group: "action-types" },
                  class: "action-type-filter") %>
            <%= apple_filter_button("Tasks",
                  icon: "fa-light fa-list-check",
                  color: "blue",
                  active: false,
                  data: { filter: "task", group: "action-types" },
                  class: "action-type-filter") %>
            <%= apple_filter_button("Reminders",
                  icon: "fa-light fa-bell",
                  color: "purple",
                  active: false,
                  data: { filter: "reminder", group: "action-types" },
                  class: "action-type-filter") %>
            <%= apple_filter_button("Alerts",
                  icon: "fa-light fa-exclamation-triangle",
                  color: "red",
                  active: false,
                  data: { filter: "alert", group: "action-types" },
                  class: "action-type-filter") %>
            <%= apple_filter_button("Callbacks",
                  icon: "fa-light fa-phone",
                  color: "green",
                  active: false,
                  data: { filter: "callback", group: "action-types" },
                  class: "action-type-filter") %>
          </div>
          <div class="relative">
            <button class="flex items-center gap-2 bg-white/90 border border-gray-200/60 rounded-lg px-3 py-1.5 hover:bg-gray-50 transition-colors shadow-sm" type="button" id="user-filter-toggle" aria-haspopup="menu" aria-expanded="false" data-state="closed">
              <div class="flex items-center gap-2">
                <div class="relative">
                  <span class="relative flex shrink-0 overflow-hidden h-7 w-7 rounded-full" id="selected-user-avatar-container">
                    <%= render partial: 'layouts/shared/user_avatar', locals: { user: current_user, width: 28, height: 28 } %>
                  </span>
                </div>
                <span id="selected-user-name" class="text-sm font-medium text-gray-700"><%= current_user.first_name %> <%= current_user.last_name %></span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-gray-500">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <!-- User Filter Dropdown Menu -->
            <div id="user-filter-menu" class="absolute right-0 mt-2 w-64 max-h-80 overflow-y-auto rounded-md shadow-lg bg-white border border-gray-200 focus:outline-none z-[3000] hidden" role="menu" aria-orientation="vertical" aria-labelledby="user-filter-toggle">
              <div class="p-2 sticky top-0 bg-white border-b border-gray-100 z-10">
                <div class="relative">
                  <input type="text" id="user-search" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Search users...">
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="py-1" id="user-list" role="none">
                <!-- Current user (always first) -->
                <a href="#" class="user-option text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100 active-user" role="menuitem" data-user-id="<%= current_user.id %>">
                  <span class="relative flex shrink-0 overflow-hidden h-7 w-7 rounded-full mr-3">
                    <%= render partial: 'layouts/shared/user_avatar', locals: { user: current_user, width: 28, height: 28 } %>
                  </span>
                  <span class="flex-1"><%= current_user.first_name %> <%= current_user.last_name %></span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 checkmark-icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </a>
                <!-- Loading placeholder -->
                <div id="user-loading" class="px-4 py-2 text-sm text-gray-500 text-center">
                  Loading users...
                </div>
                <!-- User list will be populated here via AJAX -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="p-4 actions-list">
        <% if @actions.present? %>
        <% @actions.select { |a| !a.completed }.sort_by { |a| a.created_at }.reverse.each do |action| %>
        <%
          is_today = action.date_due.present? && action.date_due.to_date == Date.today
          date_class = is_today ? "today-action" : "other-action"
        %>
        <div class="mb-8 action-card <%= date_class %>" data-action-type="<%= action.action_type %>" data-date="<%= action.date_due.present? ? action.date_due.to_date : '' %>">
          <div
            class="action-card-container bg-gradient-to-b from-white to-gray-50/80 backdrop-blur-xl rounded-xl border border-gray-200/60 shadow-[0_4px_20px_-4px_rgba(0,0,0,0.1)] overflow-hidden flex transition-all duration-300 hover:shadow-[0_8px_30px_-4px_rgba(0,0,0,0.1)] hover:border-gray-300/60" data-action-id="<%= action.id %>">
            <%
              # Set color scheme based on action type
              color_scheme = case action.action_type
                when "task"
                  { bg: "from-blue-400 to-blue-500", icon_bg: "bg-blue-100", icon_text: "text-blue-600", icon: "list-check" }
                when "reminder"
                  { bg: "from-purple-400 to-purple-500", icon_bg: "bg-purple-100", icon_text: "text-purple-600", icon: "bell" }
                when "alerts", "alert"
                  { bg: "from-red-400 to-red-500", icon_bg: "bg-red-100", icon_text: "text-red-600", icon: "exclamation-triangle" }
                when "comment"
                  { bg: "from-blue-400 to-blue-500", icon_bg: "bg-blue-100", icon_text: "text-blue-600", icon: "message-square" }
                when "callback"
                  { bg: "from-green-400 to-green-500", icon_bg: "bg-green-100", icon_text: "text-green-600", icon: "phone" }
                else
                  { bg: "from-gray-400 to-gray-500", icon_bg: "bg-gray-100", icon_text: "text-gray-600", icon: "circle" }
              end

              # Set priority badge styling
              priority_style = case action.priority
                when "urgent"
                  { bg: "bg-red-100/90", text: "text-red-700", border: "border-red-300/70" }
                when "high"
                  { bg: "bg-orange-50/80", text: "text-orange-600", border: "border-orange-200/60" }
                when "medium"
                  { bg: "bg-amber-50/80", text: "text-amber-600", border: "border-amber-200/60" }
                else # low
                  { bg: "bg-blue-50/80", text: "text-blue-600", border: "border-blue-200/60" }
              end
            %>
            <div class="w-1.5 bg-gradient-to-b <%= color_scheme[:bg] %> rounded-tl-xl rounded-bl-xl"></div>
            <div class="flex-1">
              <div class="px-4 pt-4 pb-2 flex items-center justify-between">
                <div class="flex items-center gap-4">
                  <div class="w-6 h-6 rounded-full <%= color_scheme[:icon_bg] %> flex items-center justify-center">
                    <% if color_scheme[:icon] == "list-check" %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list-checks h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <path d="m3 7 3 3 3-3"/>
                        <path d="M21 11H11"/>
                        <path d="m3 17 3 3 3-3"/>
                        <path d="M21 21H11"/>
                      </svg>
                    <% elsif color_scheme[:icon] == "bell" %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                        <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"/>
                      </svg>
                    <% elsif color_scheme[:icon] == "exclamation-triangle" %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                        <path d="M12 9v4"/>
                        <path d="M12 17h.01"/>
                      </svg>
                    <% elsif color_scheme[:icon] == "message-square" %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                      </svg>
                    <% elsif color_scheme[:icon] == "phone" %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                      </svg>
                    <% else %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle h-3.5 w-3.5 <%= color_scheme[:icon_text] %>">
                        <circle cx="12" cy="12" r="10"/>
                      </svg>
                    <% end %>
                  </div>
                  <span class="text-sm font-semibold uppercase text-gray-500"><%= action.action_type.capitalize %></span>
                </div>
                <div class="flex items-center gap-2">
                  <% if action.priority.present? %>
                    <div class="inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-full <%= priority_style[:bg] %> <%= priority_style[:text] %> <%= priority_style[:border] %> backdrop-blur-sm" data-v0-t="badge">
                      <%= action.priority.capitalize %>
                    </div>
                  <% end %>

                  <% if action.date_due.present? %>
                    <%
                      days_overdue = (Date.today - action.date_due.to_date).to_i
                      overdue_class = days_overdue > 0 ? "text-red-600" : "text-green-600"
                      overdue_text = days_overdue > 0 ? "#{days_overdue} days overdue" : "#{days_overdue.abs} days remaining"
                    %>
                    <span class="text-xs font-medium px-2 py-1 rounded-full bg-amber-50/80 text-amber-700 border border-amber-200/60 backdrop-blur-sm flex items-center gap-1">
                      Due: <%= action.date_due.strftime("%d %b %Y") %>
                      <span class="inline-block h-1 w-1 bg-amber-300 rounded-full mx-1"></span>
                      <span class="font-semibold <%= overdue_class %>"><%= overdue_text %></span>
                    </span>
                  <% end %>
                </div>
              </div>
              <div class="p-4">
                <div class="flex justify-between items-start">
                  <div>
                    <% if action.actionable.present? && action.actionable.respond_to?(:name) %>
                      <h3 class="font-medium mb-1"><%= action.actionable.name %></h3>
                    <% elsif action.patient.present? %>
                      <h3 class="font-medium mb-1"><%= action.patient.full_name %></h3>
                    <% else %>
                      <h3 class="font-medium mb-1">Action #<%= action.id %></h3>
                    <% end %>
                  </div>
                  <div class="group relative flex items-center gap-1">
                    <% if action.created_by_id.present? && User.find_by(id: action.created_by_id).present? %>
                      <% creator = User.find_by(id: action.created_by_id) %>
                      <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                        <%= render 'layouts/shared/user_avatar', user: creator, width: 32, height: 32, show_initials: true, tippy: true %>
                      </span>
                    <% end %>

                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-chevron-right h-4 w-4 text-gray-400">
                      <path d="m9 18 6-6-6-6"></path>
                    </svg>

                    <div class="flex -space-x-2">
                      <% if action.assigned_to_json.present? %>
                        <%
                          begin
                            # Handle both string and array formats
                            assigned_users = if action.assigned_to_json.is_a?(String)
                              JSON.parse(action.assigned_to_json).reject(&:blank?).first(3)
                            elsif action.assigned_to_json.is_a?(Array)
                              action.assigned_to_json.reject(&:blank?).first(3)
                            else
                              []
                            end
                          rescue JSON::ParserError
                            assigned_users = []
                          end
                        %>
                        <% assigned_users.each do |user_id| %>
                          <% user = User.find_by(id: user_id) %>
                          <% if user.present? %>
                            <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                              <%= render 'layouts/shared/user_avatar', user: user, width: 32, height: 32, show_initials: true, tippy: true %>
                            </span>
                          <% end %>
                        <% end %>

                        <%
                          begin
                            # Handle both string and array formats for total count
                            total_assigned = if action.assigned_to_json.is_a?(String)
                              JSON.parse(action.assigned_to_json).reject(&:blank?).size
                            elsif action.assigned_to_json.is_a?(Array)
                              action.assigned_to_json.reject(&:blank?).size
                            else
                              0
                            end
                          rescue JSON::ParserError
                            total_assigned = 0
                          end
                        %>
                        <% if total_assigned > 3 %>
                          <div class="flex items-center justify-center h-8 w-8 bg-gray-100 rounded-full border-2 border-white shadow-sm">
                            <span class="text-xs text-gray-500">+<%= total_assigned - 3 %></span>
                          </div>
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                </div>

                <% if action.description.present? %>
                  <p class="text-sm text-gray-600 mb-4 mt-4 w-full"><%= action.description %></p>
                <% else %>
                  <p class="text-sm text-gray-400 italic mb-4 mt-4 w-full">No description provided</p>
                <% end %>
              </div>
              <div class="flex items-center justify-between gap-2 p-2 bg-gray-50"><button
                  class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 rounded-full relative backdrop-blur-sm bg-gradient-to-r from-blue-50/80 to-blue-100/60 text-blue-500 hover:text-blue-600 hover:bg-blue-100/70 border border-blue-100/40 transition-all duration-200 shadow-sm"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-clock h-4 w-4">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg></button>
                <div class="flex items-center gap-2"><button
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background hover:text-accent-foreground px-3 h-8 rounded-full relative backdrop-blur-sm hover:bg-gray-100/80 border-gray-200/60"
                    data-action-id="<%= action.id %>"><svg
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-message-square h-3.5 w-3.5 mr-1">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>Comments
                    <% comment_count = action.action_comments.where.not(comment: [nil, '']).count %>
                    <% if comment_count > 0 %>
                      <span class="absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white"><%= comment_count %></span>
                    <% end %></button><button
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full text-amber-600 border-amber-200/70 bg-amber-50/80 hover:bg-amber-100/80 backdrop-blur-sm transition-all duration-200"
                    type="button" id="reminder-dropdown-<%= action.id %>" aria-haspopup="menu" aria-expanded="false" data-state="closed" data-action-id="<%= action.id %>"><svg
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-clock h-3.5 w-3.5 mr-1">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>Reminder</button>
                    <button
                    data-action-id="<%= action.id %>"
                    data-action-type="<%= action.action_type %>"
                    class="complete-action-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full text-emerald-600 border-emerald-200 bg-emerald-50/80 hover:bg-emerald-100/80 backdrop-blur-sm transition-all duration-200">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big h-3.5 w-3.5 mr-1.5">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <path d="m9 11 3 3L22 4"></path>
                      </svg><%= %w[complaint alert].include?(action.action_type) ? 'Resolve' : 'Complete' %>
                    </button></div>
              </div>
            </div>
          </div>
        </div>
        <% end %>
        <% end %>

        <div class="empty-state flex flex-col items-center justify-center p-8 text-center <%= @actions.any? { |a| !a.completed } ? 'hidden' : 'flex' %>">
          <i class="fa-light fa-tasks"></i>
          <p>No actions yet</p>
        </div>
      </div>
      <!-- Completed actions section -->
      <div class="completed-actions-container" style="display: none;">


        <% if @actions.present? && @actions.any? { |a| a.completed } %>
          <div class="px-4 space-y-4">
            <% @actions.select { |a| a.completed }.sort_by { |a| a.updated_at }.reverse.each do |action| %>
              <div class="action-card completed" data-action-type="<%= action.action_type %>">
                <div class="action-card-container bg-gradient-to-b from-white to-gray-50/80 backdrop-blur-xl rounded-xl border border-gray-200/60 shadow-sm overflow-hidden flex transition-all duration-300 hover:shadow-md hover:border-gray-300/60" data-action-id="<%= action.id %>">
                  <div class="w-1.5 bg-gradient-to-b from-green-400 to-green-500 rounded-tl-xl rounded-bl-xl"></div>
                  <div class="flex-1">
                    <div class="px-4 pt-4 pb-2 flex items-center justify-between">
                      <div class="flex items-center gap-4">
                        <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                          <i class="fa-light fa-check h-3.5 w-3.5 text-green-600"></i>
                        </div>
                        <span class="text-sm font-semibold uppercase text-gray-500"><%= action.action_type.titleize %></span>
                      </div>

                      <span class="text-xs font-medium px-2 py-1 rounded-full bg-green-50/80 text-green-700 border border-green-200/60 backdrop-blur-sm flex items-center gap-1">
                        Completed: <%= action.updated_at.strftime("%d %b %Y") %>
                      </span>
                    </div>

                    <div class="p-4">
                      <div class="flex justify-between items-start">
                        <div>
                          <p class="text-sm text-gray-600 mb-2"><%= action.description %></p>
                        </div>

                        <% if action.created_by.present? || (action.assigned_to_json.present? && action.assigned_to_json.any?) %>
                          <div class="group relative flex items-center gap-1">
                            <% if action.created_by.present? %>
                              <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                                <%= render partial: 'layouts/shared/user_avatar', locals: { user: action.created_by, width: 32, height: 32, tippy: true } %>
                              </span>
                            <% end %>

                            <% if action.created_by.present? && action.assigned_to_json.present? && action.assigned_to_json.any? %>
                              <i class="fa-light fa-chevron-right h-4 w-4 text-gray-400"></i>
                            <% end %>

                            <% if action.assigned_to_json.present? && action.assigned_to_json.any? %>
                              <div class="flex -space-x-2">
                                <% User.where(id: action.assigned_to_json).each do |user| %>
                                  <span class="relative flex shrink-0 overflow-hidden h-8 w-8 border-2 border-white shadow-sm rounded-full">
                                    <%= render partial: 'layouts/shared/user_avatar', locals: { user: user, width: 32, height: 32, tippy: true } %>
                                  </span>
                                <% end %>
                              </div>
                            <% end %>

                            <div class="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-white/90 backdrop-blur-md p-3 rounded-xl shadow-lg border border-gray-100/60 opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap z-10 w-max max-w-[280px]">
                              <div class="space-y-2">
                                <% if action.created_by.present? %>
                                  <div>
                                    <p class="text-xs font-medium text-gray-500 mb-1">Assigned by</p>
                                    <div class="flex items-center gap-2">
                                      <span class="relative flex shrink-0 overflow-hidden h-5 w-5 rounded-full">
                                        <%= render partial: 'layouts/shared/user_avatar', locals: { user: action.created_by, width: 20, height: 20 } %>
                                      </span>
                                      <p class="text-sm font-medium"><%= action.created_by.full_name %></p>
                                    </div>
                                  </div>
                                <% end %>

                                <% if action.created_by.present? && action.assigned_to_json.present? && action.assigned_to_json.any? %>
                                  <div class="h-px bg-gray-100"></div>
                                <% end %>

                                <% if action.assigned_to_json.present? && action.assigned_to_json.any? %>
                                  <div>
                                    <p class="text-xs font-medium text-gray-500 mb-1">Assigned to</p>
                                    <div class="flex flex-col gap-1.5">
                                      <% User.where(id: action.assigned_to_json).each do |user| %>
                                        <div class="flex items-center gap-2">
                                          <span class="relative flex shrink-0 overflow-hidden h-5 w-5 rounded-full">
                                            <%= render partial: 'layouts/shared/user_avatar', locals: { user: user, width: 20, height: 20 } %>
                                          </span>
                                          <p class="text-sm font-medium"><%= user.full_name %></p>
                                        </div>
                                      <% end %>
                                    </div>
                                  </div>
                                <% end %>
                              </div>
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>

                    <div class="flex items-center justify-between gap-2 p-2 bg-gray-50">
                      <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-8 w-8 rounded-full relative backdrop-blur-sm bg-gradient-to-r from-blue-50/80 to-blue-100/60 text-blue-500 hover:text-blue-600 hover:bg-blue-100/70 border border-blue-100/40 transition-all duration-200 shadow-sm">
                        <i class="fa-light fa-clock h-4 w-4"></i>
                      </button>

                      <div class="flex items-center gap-2">
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:text-accent-foreground px-3 h-8 rounded-full relative backdrop-blur-sm hover:bg-gray-100/80 border-gray-200/60"
                                data-action-id="<%= action.id %>">
                          <i class="fa-light fa-message-lines h-3.5 w-3.5 mr-1"></i>
                          Comments
                          <% comment_count = action.action_comments.where.not(comment: [nil, '']).count %>
                          <% if comment_count > 0 %>
                            <span class="absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white"><%= comment_count %></span>
                          <% end %>
                        </button>

                        <button data-action-id="<%= action.id %>" class="uncomplete-action-btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full text-gray-600 border-gray-200 bg-white hover:bg-gray-50 backdrop-blur-sm transition-all duration-200">
                          <i class="fa-light fa-rotate-left h-3.5 w-3.5 mr-1.5"></i>
                          Reopen
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Commented out to prevent conflicts with actions_sidebar.js
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables first - MUST be at the top
    let currentActionTypeFilter = 'all'; // Track current action type filter
    let currentTabFilter = 'all'; // Track current tab (today/all/completed)

    // Tab switching functionality for Today/All Actions/Completed tabs
    const tabButtons = document.querySelectorAll('.tab');
    const completedActionsContainer = document.querySelector('.completed-actions-container');
    const actionsListContainer = document.querySelector('.actions-list');





    // Function to handle tab switching
    function handleTabSwitch(clickedTab) {
      // Clean the ID by removing any extra quotes
      const cleanId = clickedTab.id.replace(/['"]/g, '');

      // Use the Apple filter button manager to handle styling
      if (window.appleFilterButtons) {
        window.appleFilterButtons.setActiveButton(clickedTab);
      }

      // Handle the Today/All tabs
      if (cleanId === 'today-tab' || cleanId === 'all-tab') {
        // Show the actions list and hide completed actions
        completedActionsContainer.style.display = 'none';
        actionsListContainer.style.display = 'block';

        // Update the current tab filter
        currentTabFilter = cleanId === 'today-tab' ? 'today' : 'all';

        // Apply both filters
        applyFilters();
      }
      // Handle the Completed tab
      else if (cleanId === 'completed-tab') {
        // Show completed actions and hide the regular actions list
        if (completedActionsContainer) {
          completedActionsContainer.style.display = 'block';
        }

        if (actionsListContainer) {
          actionsListContainer.style.display = 'none';
        }

        // Update the current tab filter
        currentTabFilter = 'completed';

        // Apply filters to completed actions
        applyFilters();
      }
    }

    // Add click event listeners to all tab buttons
    tabButtons.forEach(tab => {
      tab.addEventListener('click', function() {
        handleTabSwitch(this);
      });
    });

    // Initialize defaults with a small delay to ensure Apple filter button manager is ready
    setTimeout(() => {
      // Set initial filter states explicitly
      currentTabFilter = 'all';
      currentActionTypeFilter = 'all';

      // Initialize with the default tab (All Actions) and ensure it's visually active
      const allTab = document.getElementById('all-tab');
      if (allTab) {
        // Ensure the All Actions tab is visually active first
        if (window.appleFilterButtons) {
          window.appleFilterButtons.setActiveButton(allTab);
        }
      }

      // Initialize the action type filter to 'all' by default and ensure it's visually active
      const allFilterButton = document.querySelector('[data-filter="all"]');
      if (allFilterButton && window.appleFilterButtons) {
        window.appleFilterButtons.setActiveButton(allFilterButton);
      }

      // Ensure the correct containers are visible
      if (completedActionsContainer) {
        completedActionsContainer.style.display = 'none';
      }
      if (actionsListContainer) {
        actionsListContainer.style.display = 'block';
      }

      // Apply filters to ensure proper initial state
      applyFilters();
    }, 100);

    // Filter tabs functionality (All/Tasks/Reminders/Alerts/Callbacks)
    const filterButtons = document.querySelectorAll('.action-type-filter');

    // Function to apply both filters together
    function applyFilters() {
      if (currentTabFilter === 'completed') {
        // Apply filters to completed actions
        const completedActionCards = document.querySelectorAll('.action-card.completed');

        completedActionCards.forEach(card => {
          const cardActionType = card.getAttribute('data-action-type');

          // Check if it passes the action type filter
          const actionTypeMatch = currentActionTypeFilter === 'all' || cardActionType === currentActionTypeFilter;

          // Show the card only if it passes the filter
          if (actionTypeMatch) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      } else {
        // Apply filters to regular (non-completed) actions
        const actionCards = document.querySelectorAll('.action-card:not(.completed)');

        actionCards.forEach(card => {
          const cardActionType = card.getAttribute('data-action-type');

          // First check if it passes the action type filter
          const actionTypeMatch = currentActionTypeFilter === 'all' || cardActionType === currentActionTypeFilter;

          // Then check if it passes the tab filter (today/all)
          let tabMatch = true;
          if (currentTabFilter === 'today') {
            tabMatch = card.classList.contains('today-action');
          }

          // Show the card only if it passes both filters
          if (actionTypeMatch && tabMatch) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      }
    }
  });
</script>
-->

<% # Render the shared modals %>
<%
  # Determine the appropriate actionable based on the current context
  if defined?(@card) && @card.present?
    actionable_type = "CrmCard"
    actionable_id = @card.id
    patient_id = @card.patient&.id
  elsif defined?(@patient) && @patient.present?
    actionable_type = "Patient"
    actionable_id = @patient.id
    patient_id = @patient.id
  else
    actionable_type = "User"
    actionable_id = current_user.id
    patient_id = nil
  end
%>
<%= render partial: "admin/shared/modals/new_action_modal", locals: {
  modal_id: "sidebarNewActionModal",
  actionable_type: actionable_type,
  actionable_id: actionable_id,
  patient_id: patient_id,
  show_practice_patient_toggle: true
} %>

<%= render partial: "admin/shared/modals/action_comment_modal", locals: {
  modal_id: "newActionCommentModal"
} %>

