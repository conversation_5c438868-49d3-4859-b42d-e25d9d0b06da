<div class="relative" data-notification-id="<%= notification.id %>" data-date="<%= notification.created_at.strftime('%Y-%m-%d') %>">
  <div class="relative pt-0 pb-0">
    <div class="absolute inset-0 z-0">
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#E6F0FF] shadow-sm" style="transform: translateY(4px); z-index: -1; margin-left: 2px; margin-right: 2px;"></div>
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#E6F0FF] shadow-sm" style="transform: translateY(8px); z-index: -2; margin-left: 4px; margin-right: 4px;"></div>
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#E6F0FF] shadow-sm" style="transform: translateY(12px); z-index: -3; margin-left: 6px; margin-right: 6px;"></div>
    </div>
    <div class="w-full rounded-xl shadow-[0_2px_10px_-3px_rgba(0,0,0,0.1)] hover:shadow-[0_8px_30px_-10px_rgba(77,124,254,0.2)] border border-[#E6F0FF] transition-all duration-300 relative z-10 bg-white opacity-100 translate-x-0">
      <div class="flex flex-col w-full overflow-hidden rounded-xl">
        <div class="w-full h-1 bg-gradient-to-r from-[#4D7CFE] to-[#56CCF2]"></div>
        <div class="w-full bg-gradient-to-b from-[#F0F7FF] to-[#F8FBFF]">
          <div class="relative z-10 p-3">
            <div class="flex justify-between items-center mb-1.5">
              <div class="flex items-center gap-3">
                <div class="relative">
                  <div class="w-10 h-10 rounded-full bg-gray-200 border border-gray-300 overflow-hidden relative">
                    <% if notification.data['avatar_url'].present? %>
                      <img alt="<%= notification.sender || 'User' %>" class="w-full h-full object-cover" src="<%= notification.data['avatar_url'] %>">
                    <% else %>
                      <div class="w-full h-full flex items-center justify-center bg-blue-50">
                        <i class="fa-thin fa-message text-[#4D7CFE] text-xl"></i>
                      </div>
                    <% end %>
                  </div>
                  <div class="absolute -bottom-1 -right-1 w-5 h-5 flex items-center justify-center rounded-sm bg-[#D6E6FF] border border-[#B8D4FF] shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="#4D7CFE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </div>
                </div>
                <div class="flex flex-col">
                  <h3 class="font-medium text-[16px] text-[#1A1A2E] tracking-tight"><%= notification.sender || 'Unknown' %></h3>
                </div>
              </div>
              <div class="flex items-center px-2 py-1 rounded-md bg-[#D6E6FF] border border-[#B8D4FF]">
                <span class="w-1.5 h-1.5 rounded-full bg-[#4D7CFE] animate-pulse mr-1.5"></span>
                <span class="text-[12px] font-medium text-[#1E40AF]">New SMS</span>
              </div>
            </div>
            <p class="text-[14px] text-[#4E5D78] leading-snug mt-0.5 mb-1 w-full line-clamp-2">
              <%= notification.message || notification.description %>
            </p>
            <div class="flex justify-between items-center mt-2">
              <div class="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#64748B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span class="text-[11px] text-[#64748B]"><%= formatted_notification_time(notification) %></span>
              </div>
              <button class="px-2.5 py-1 bg-[#F5F9FF] text-[#4D7CFE] text-[12px] font-medium rounded hover:bg-[#EDF4FF] transition-colors flex items-center gap-1">Options
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down transition-transform ">
                  <path d="m6 9 6 6 6-6"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="absolute top-[-10px] right-[-10px] z-[100]">
      <button class="w-[24px] h-[24px] rounded-full bg-[#EBF3FF] flex items-center justify-center hover:bg-[#D6E6FF] transition-colors shadow-sm border border-[#D6E6FF]" aria-label="Close notification">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x text-[#4D7CFE]">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
    </div>
  </div>
</div>
