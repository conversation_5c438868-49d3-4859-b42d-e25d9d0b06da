# frozen_string_literal: true

module Admin
  module Patients
    class ChartingController < Admin::ApplicationController
      def show
        @patient = Patient.find(params[:patient_id])
        self.page_title = "Charting – #{@patient.full_name_with_title}"

        @invoices = @patient.invoices.includes(:invoice_items)
        @unpaid_invoices = @invoices.where(status: 'unpaid')
        @paid_invoices = @invoices.where(status: 'paid')
        @part_paid_invoices = @invoices.where(status: 'part-paid')

        @medical_histories = @patient.medical_histories.order(created_at: :desc)

        @xrays = @patient.xrays.includes(developed_by: { image_attachment: :blob }, taken_by: { image_attachment: :blob },
                                         charting_appointment: %i[course_of_treatment calendar_booking], file_attachment: :blob)
        @opgs = @patient.opgs.includes(developed_by: { image_attachment: :blob }, taken_by: { image_attachment: :blob },
                                       charting_appointment: %i[course_of_treatment calendar_booking], file_attachment: :blob)
        @cbcts = @patient.cbcts.includes(developed_by: { image_attachment: :blob }, taken_by: { image_attachment: :blob },
                                         charting_appointment: %i[course_of_treatment calendar_booking], file_attachment: :blob)
        @stls = @patient.stls.includes(developed_by: { image_attachment: :blob },
                                       charting_appointment: %i[course_of_treatment calendar_booking], file_attachment: :blob)

        @practitioner_users = User.all
        @nurse_users = User.all

        @course_of_treatments = @patient.course_of_treatments
                                        .includes(charting_appointments: :dentist)
                                        .where(archived: false)
                                        .order(created_at: :desc)

        if @course_of_treatments.empty?
          @patient.course_of_treatments.create!(
            name: 'Holding Treatment Plan - Use COT Settings To Edit',
            lead_clinician_id: current_user.id,
            dentist_id: current_user.id,
            practice_id: @patient.current_practice_id
          )

          @course_of_treatments = @patient.course_of_treatments
                                          .includes(charting_appointments: :dentist)
                                          .where(archived: false)
                                          .order(created_at: :desc)
        end

        @bpe = @patient.bpes.new
        @bewe = @patient.bewes.new
        @bpe_history = @patient.bpes.includes(completed_by: { image_attachment: :blob }).order(created_at: :desc)
        @bewe_history = @patient.bewes.includes(completed_by: { image_attachment: :blob }).order(created_at: :desc)

        @course_of_treatment =
          @course_of_treatments.find_by(id: params[:course_of_treatment_id]) || @course_of_treatments.first

        @current_practice = @course_of_treatment.practice

        @cot_payment_plans = CotPaymentPlan.where(practice_id: @current_practice.id)
        @cot_payment_plan_treatments = CotPaymentPlansTreatment.all

        @completed_charted_treatments = ChartedTreatment.joins(charting_appointment: :course_of_treatment)
                                                        .where(course_of_treatment:
                                                                 { id: @course_of_treatments.select(:id) })
                                                        .where.not(completed_at: nil)
                                                        .includes(charting_appointment: :course_of_treatment)
        @treatment_categories = TreatmentCategory.includes(treatments: :practice).load_async
        @favorite_treatments = current_user.favorite_treatments.includes(:practice, :treatment_category).load_async
        @base_treatments = @patient.charting_base_chart.charted_treatments.includes(:treatment, :practitioner,
                                                                                    :charting_appointment).load_async

        @completed_treatments = ChartedTreatment
                                .joins(charting_appointment: :course_of_treatment)
                                .includes(:treatment, :practitioner)
                                .where(course_of_treatments: { patient_id: @patient.id })
                                .where.not(completed: false)
                                .where.not(charting_appointment_id: nil)
                                .order(completed_at: :desc)
                                .load_async

        @archived_cots = @patient.course_of_treatments.where(archived: true).order(created_at: :desc)
        @lab_dockets = @patient.lab_works.includes([:lab_dockets]).load_async.map(&:lab_dockets).flatten
        @charting_appointments = @course_of_treatment.charting_appointments
                                                     .includes(:dentist, :appointment_notes, :lab_works, :calendar_booking,
                                                               charted_treatments: [:treatment, :nurse, :referrer, :invoice,
                                                                                    { practitioner: { image_attachment: :blob } }])
                                                     .load_async
                                                     .order(:position)

        @history_items = @completed_treatments.includes(:treatment, :practitioner, charting_appointment: :dentist).order(:created_at)

        @practice_cot_template_notes = CotTemplateNote.where(practice_id: Current.practice_id || current_user.practice_ids).load_async
        @user_cot_template_notes = CotTemplateNote.where(user_id: current_user.id).load_async
      end

      def create_appointment
        @patient = Patient.find(params[:patient_id])

        @appointment = ChartingAppointment.new(charting_appointment_params)
        @invoices = @patient.invoices.includes(:invoice_items)
        @unpaid_invoices = @invoices.where(status: 'unpaid')
        @paid_invoices = @invoices.where(status: 'paid')
        @part_paid_invoices = @invoices.where(status: 'part-paid')

        @appointment.dentist = current_user

        # Handle position insertion logic
        if @appointment.position.present?
          course_of_treatment = CourseOfTreatment.find(@appointment.course_of_treatment_id)

          # Increment positions of appointments that come after the insertion position
          course_of_treatment.charting_appointments
                             .where('position >= ?', @appointment.position)
                             .update_all('position = position + 1')
        end

        if @appointment.save
          course_of_treatment = @appointment.course_of_treatment
          idx = course_of_treatment.charting_appointments.count - 1
          render json: {
            success: true,
            html: render_to_string(partial: 'admin/patients/charting/course_of_treatments/appointment',
                                   locals: { appt: @appointment, idx: idx }),
            new_appointment_id: @appointment.id
          }
        else
          render json: { success: false, error: @appointment.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def create_course_of_treatment
        @patient = Patient.find(params[:patient_id])

        @course_of_treatment = CourseOfTreatment.create!(
          patient: @patient,
          dentist: current_user,
          name: params[:name],
          practice_id: params[:practice_id],
          lead_clinician_id: params[:lead_clinician_id],
          cot_category_id: params[:cot_category_id]
        )

        @charting_appointment = @course_of_treatment.charting_appointments.create!(
          dentist_id: current_user.id
        )

        render json: {
          redirect_url: admin_patient_charting_path(@patient, course_of_treatment_id: @course_of_treatment.id)
        }
      end

      def load_course_of_treatment_form_data
        @patient = Patient.find(params[:patient_id])

        practices = policy_scope(Practice).select(:id, :name)
        clinicians = policy_scope(User).map do |user|
          { name: user.full_name, id: user.id }
        end
        cot_categories = policy_scope(CotCategory).all.map do |tc|
          { name: tc.name, id: tc.id }
        end

        render json: {
          practices: practices.select(:id, :name),
          cot_categories: cot_categories,
          clinicians: clinicians,
          patient_current_practice_id: @patient.current_practice_id
        }
      end

      def create_treatment
        tooth_id = params[:tooth_id]
        surface_id = params[:surface_id]
        appointment_id = params[:appointment_id]
        treatment_id = params[:treatment_id]
        multi_click = params[:multi_click].to_s.downcase == 'true'
        surface_order = params[:surface_order]&.to_i

        appointment = ChartingAppointment.find(appointment_id)

        index = appointment.charted_treatments.count

        charted_treatment = if surface_id.present? && !multi_click
                              ChartedTreatment.new(
                                charting_appointment_id: appointment_id,
                                position: tooth_id,
                                treatment_id: treatment_id,
                                child_tooth_flag: params[:child_tooth],
                                pricing_type: params[:pricing_type],
                                practitioner: current_user
                              )
                            else
                              ChartedTreatment.find_or_initialize_by(
                                charting_appointment_id: appointment_id,
                                position: tooth_id,
                                treatment_id: treatment_id,
                                child_tooth_flag: params[:child_tooth],
                                pricing_type: params[:pricing_type],
                                practitioner: current_user
                              )
                            end

        if charted_treatment.surface.nil?
          charted_treatment.surface = { surface_id => surface_order }
        else
          # TODO: We need to change `surface` DB column from String to JSON
          # and write a data migration. Using eval() is a dangerous temporary hack
          surface_hash = eval(charted_treatment.surface)
          surface_hash[surface_id] = surface_order
          charted_treatment.surface = surface_hash
        end

        if charted_treatment.treatment.is_full_tooth?
          if !charted_treatment.persisted? &&
             appointment.charted_treatments.where(
               position: charted_treatment.position,
               treatment_id: charted_treatment.treatment_id,
               child_tooth_flag: charted_treatment.child_tooth_flag,
               pricing_type: charted_treatment.pricing_type,
               practitioner: current_user
             ).exists?
            render json: { success: true, error: 'This treatment has already been assigned to this tooth' }, status: :ok
            return
          end
        elsif !charted_treatment.persisted? && appointment.charted_treatments
                                                          .where(position: charted_treatment.position,
                                                                 treatment_id: charted_treatment.treatment_id,
                                                                 surface: charted_treatment.surface,
                                                                 child_tooth_flag: charted_treatment.child_tooth_flag,
                                                                 pricing_type: charted_treatment.pricing_type,
                                                                 practitioner: current_user).exists?
          render json: { success: true, error: 'This treatment has already been assigned to this tooth' }, status: :ok
          return
        end

        if charted_treatment.save!
          render partial: 'admin/patients/charting/course_of_treatments/charted_treatment',
                 locals: { charted_treatment: charted_treatment, index: index, is_new: true }, status: :ok
        else
          render json: { success: false, error: charted_treatment.errors.full_messages }, status: :unprocessable_entity
        end
      rescue StandardError => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def load_charted_treatments
        @patient = Patient.find(params[:patient_id])
        @course_of_treatments = @patient.course_of_treatments
                                        .includes(charting_appointments: :dentist)
                                        .where(archived: false)
                                        .order(created_at: :desc)

        @course_of_treatment =
          @course_of_treatments.find_by(id: params[:course_of_treatment_id]) || @course_of_treatments.first

        charted_treatments = @course_of_treatment.charting_appointments.flat_map do |charting_appointment|
          charting_appointment.charted_treatments.map do |ct|
            {
              tooth_position: ct.position,
              treatment_folder: (ct.treatment.treatment_folder || ct.treatment.patient_friendly_name).downcase,
              treatment_id: ct.treatment_id,
              ctid: ct.id,
              is_full_tooth_treatment: ct.treatment.is_full_tooth?,
              is_completed: ct.completed_at.present?,
              surface: ct.surface_name
            }
          end
        end

        render json: charted_treatments
      end

      def load_base_treatments
        @patient = Patient.find(params[:patient_id])

        charted_treatments = @patient.charting_base_chart.charted_treatments.includes(:treatment).map do |ct|
          {
            tooth_position: ct.position || '',
            treatment_folder: (ct.treatment.treatment_folder || ct.treatment.patient_friendly_name).downcase,
            treatment_id: ct.treatment_id,
            missing_tooth: ct.treatment.missing_tooth,
            ctid: ct.id,
            is_full_tooth_treatment: ct.treatment.is_full_tooth?,
            is_completed: ct.completed_at.present?,
            surface: ct.surface_name
          }
        end

        render json: charted_treatments
      end

      def load_history_treatments
        @patient = Patient.find(params[:patient_id])

        history_treatments = ChartedTreatment
                             .joins(charting_appointment: :course_of_treatment)
                             .includes(:treatment)
                             .where(course_of_treatments: { patient_id: @patient.id })
                             .where.not(completed: false)
                             .where.not(charting_appointment_id: nil)
                             .order(Arel.sql('CAST(course_of_treatments.completed_at AS timestamp) DESC'))
                             .map do |ct|
          {
            tooth_position: ct.position,
            treatment_name: (ct.treatment.treatment_folder || ct.treatment.patient_friendly_name).downcase,
            is_full_tooth_treatment: ct.is_full_tooth_treatment?,
            surface: ct.surface_name,
            treatment_id: ct.treatment_id,
            id: ct.id,
            remove_treatment_when_completed: ct.treatment.remove_treatment_when_completed,
            remove_tooth_when_completed: ct.treatment.remove_tooth_when_completed
          }
        end

        render json: history_treatments
      end

      def delete_treatment
        charted_treatment = ChartedTreatment.find(params[:treatment_id])

        if charted_treatment.destroy
          render json: { success: true }
        else
          render json: { success: false, error: charted_treatment.errors.full_messages.join(', ') }, status: :unprocessable_entity
        end
      end

      def reorder_charted_treatments
        @charted_treatment = ChartedTreatment.find_by(id: params[:id])
        new_appointment = ChartingAppointment.find_by(id: params[:charting_appointment_id])

        if @charted_treatment.nil?
          render json: { error: 'Invalid charted treatment' }, status: :unprocessable_entity
          return
        end

        if new_appointment.nil?
          render json: { error: 'Invalid appointment' }, status: :unprocessable_entity
          return
        end

        @charted_treatment.update(charting_appointment: new_appointment, position_order: params[:position_order].to_i)
        @charted_treatment.charting_appointment.charted_treatments.where.not(id: @charted_treatment.id).find_each do |ct|
          ct.set_list_position(ct.position_order)
        end

        render json: { message: 'Reorder successful' }, status: :ok
      end

      def reorder_appointments
        course_of_treatment = CourseOfTreatment.find(params[:course_of_treatment_id])
        appointment_ids = params[:appointment_ids]

        if appointment_ids.blank?
          render json: { error: 'No appointment IDs provided' }, status: :unprocessable_entity
          return
        end

        ActiveRecord::Base.transaction do
          appointment_ids.each_with_index do |appointment_id, index|
            appointment = course_of_treatment.charting_appointments.find(appointment_id)
            appointment.set_list_position(index + 1)
          end
        end

        render json: { success: true }
      rescue StandardError => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def update_charted_treatment
        @charted_treatment = ChartedTreatment.find(params[:charted_treatment][:id])
        if @charted_treatment.update(update_details_params)
          if @charted_treatment.completed? && update_details_params[:completed].present?
            @charted_treatment.update(completed_at: Time.current)
          else
            @charted_treatment.update(completed_at: nil)
          end

          render json: { success: true, practitioner_avatar: @charted_treatment.practitioner&.image&.url }
        else
          render json: { success: false, error: @charted_treatment.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def assign_base_treatment
        patient = Patient.find(params[:patient_id])
        tooth = params[:tooth_id]
        treatment = params[:treatment_id]
        surface = params[:surface_id]
        multi_click = params[:multi_click] == 'true'
        surface_order = params[:surface_order]&.to_i

        # Create a 'ghost' appointment for base chart treatments if it doesn't exist
        base_chart = patient.charting_base_chart
        index = base_chart.charted_treatments.count

        charted_treatment = if surface.present? && !multi_click
                              base_chart.charted_treatments.new(
                                position: tooth,
                                treatment_id: treatment,
                                practitioner: current_user,
                                pricing_type: params[:pricing_type]
                              )
                            else
                              # Find existing charted treatment or create a new one
                              base_chart.charted_treatments.find_or_initialize_by(
                                position: tooth,
                                treatment_id: treatment,
                                practitioner: current_user,
                                pricing_type: params[:pricing_type]
                              )
                            end
        # Update or set the surface information
        if charted_treatment.surface.nil?
          charted_treatment.surface = { surface => surface_order }
        else
          surface_hash = eval(charted_treatment.surface)
          surface_hash[surface] = surface_order
          charted_treatment.surface = surface_hash
        end
        charted_treatment.completed_at = Time.current
        if charted_treatment.save!
          render partial: 'admin/patients/charting/course_of_treatments/base_charted_treatment',
                 locals: { app: charted_treatment, index: index }, status: :ok
        else
          render json: { success: false, message: 'Failed to assign base treatment' }, status: :unprocessable_entity
        end
      end

      def create_charting_appointment_note
        @charting_appointment = ChartingAppointment.find(params[:appointment_note][:charting_appointment_id])

        new_note = @charting_appointment.appointment_notes.new(note_params)

        new_note.update(completed_at: Time.zone.now) if new_note.completed_at.nil?

        if new_note.save
          render json: {
            success: true,
            html: render_to_string(partial: 'admin/patients/charting/course_of_treatments/appointment_note',
                                   locals: { note: new_note, appt: @charting_appointment })
          }
        else
          render json: { success: false, error: new_note.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def edit_appointment_note
        @note = AppointmentNote.find(params[:note_id])
        render json: {
          id: @note.id,
          content: @note.content,
          practitioner_id: @note.practitioner_id,
          completed_at: @note.completed_at&.strftime('%Y-%m-%d')
        }
      end

      def update_appointment_note
        @note = AppointmentNote.find(params[:appointment_note][:id])

        if @note.locked?
          render json: { success: false, error: 'Cannot edit locked appointment note' }, status: :unprocessable_entity
          return
        end

        if @note.update(note_params)
          render json: {
            success: true,
            message: 'Appointment note updated successfully',
            html: render_to_string(partial: 'admin/patients/charting/course_of_treatments/appointment_note',
                                   locals: { note: @note, appt: @note.charting_appointment })
          }
        else
          render json: { success: false, error: @note.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def delete_appointment_note
        @note = AppointmentNote.find(params[:note_id])

        if @note.locked?
          render json: { success: false, error: 'Cannot delete locked appointment note' }, status: :unprocessable_entity
          return
        end

        if @note.destroy
          render json: {
            success: true,
            message: 'Appointment note deleted successfully'
          }
        else
          render json: { success: false, error: 'Failed to delete appointment note' }, status: :unprocessable_entity
        end
      end

      def delete_appointment
        @appointment = ChartingAppointment.find(params[:appointment_id])

        deleted_treatments = @appointment.charted_treatments.map do |treatment|
          {
            id: treatment.id,
            position: treatment.position,
            treatment_id: treatment.treatment_id
          }
        end

        if @appointment.destroy
          render json: {
            success: true,
            message: 'Appointment deleted successfully',
            deleted_treatments: deleted_treatments
          }
        else
          render json: { success: false, error: @appointment.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def update_patient_payment_plan
        patient = Patient.find(params[:patient_id])
        payment_plan = CotPaymentPlan.where('LOWER(name) = ?', params[:payment_plan].to_s.downcase).first

        if patient.update(active_cot_payment_plan: payment_plan)
          render json: { success: true }
        else
          render json: { success: false, error: patient.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def get_users_list
        dentists = policy_scope(User).where(archived: false)
        render json: dentists
      end

      def change_cot
        @appointment = ChartingAppointment.find(params[:appointment_id])
        deleted_treatments = @appointment.charted_treatments.map do |treatment|
          {
            id: treatment.id,
            position: treatment.position,
            treatment_id: treatment.treatment_id
          }
        end
        new_treatment_plan = CourseOfTreatment.find(params[:cot_id])
        @new_position = new_treatment_plan.charting_appointments.maximum(:position) + 1

        if !@appointment.locked
          if @appointment.update(course_of_treatment_id: params[:cot_id], position: @new_position)
            render json: { success: true, deleted_treatments: deleted_treatments }
          else
            render json: { success: false, error: @appointment.errors.full_messages }, status: :unprocessable_entity
          end
        else
          render json: { success: false, error: 'Cannot change the treatment plan of a locked appointment' }, status: :unprocessable_entity
        end
      end

      def change_dentist
        @appointment = ChartingAppointment.find(params[:appointment_id])
        if !@appointment.locked
          if @appointment.update(dentist_id: params[:dentist_id])
            # Also update treatments if requested
            @appointment.charted_treatments.update_all(practitioner_id: params[:dentist_id]) if params[:change_treatments] == true

            render json: { success: true,
                           dentist_avatar:
                             @appointment.dentist.image.url ||
                             ActionController::Base.helpers.asset_path('default-avatar.webp') }
          else
            render json: { success: false, error: @appointment.errors.full_messages }, status: :unprocessable_entity
          end
        else
          render json: { success: false, error: 'Cannot change the dentist of a locked appointment' }, status: :unprocessable_entity
        end
      end

      def upload_photo
        @patient = Patient.find(params[:patient_id])
        uploaded_files = params[:files] || [params[:file]]

        charting_files = uploaded_files.map do |file|
          @patient.charting_files.create!(
            teeth: JSON.parse(params[:selectedTeeth]),
            justification: params[:justification],
            date_taken: params[:date_taken],
            taken_by: User.find(params[:taken_by]),
            file: file,
            charting_appointment_id: params[:appointment_id]
          )
        end

        html_content = charting_files.map do |charting_file|
          render_to_string(partial: 'admin/patients/charting/asset_card', locals: { asset: charting_file })
        end.join

        render json: { success: true, html: html_content }
      end

      def upload_stl
        @patient = Patient.find(params[:patient_id])
        uploaded_files = params[:files] || [params[:file]]

        charting_files = uploaded_files.map do |file|
          @patient.stls.create!(
            teeth: JSON.parse(params[:selectedTeeth]),
            date_taken: params[:date_taken],
            taken_by: User.find(params[:taken_by]),
            file: file,
            charting_appointment_id: params[:appointment_id]
          )
        end

        html_content = charting_files.map do |charting_file|
          render_to_string(partial: 'admin/patients/charting/asset_card', locals: { asset: charting_file })
        end.join

        render json: { success: true, html: html_content }
      end

      def upload_xray
        @patient = Patient.find(params[:patient_id])
        uploaded_files = params[:files] || []

        xrays = uploaded_files.map do |file|
          @patient.xrays.create!(
            teeth: JSON.parse(params[:selectedTeeth]),
            justification: params[:justification],
            grade: params[:grade],
            date_taken: params[:date_taken],
            description: params[:description],
            developed_by_id: params[:developed_by],
            taken_by_id: params[:taken_by],
            file: file,
            charting_appointment_id: params[:appointment_id]
          )
        end

        html_content = xrays.map do |xray|
          render_to_string(partial: 'admin/patients/charting/asset_card', locals: { asset: xray })
        end.join

        render json: { success: true, html: html_content }
      end

      def upload_opg
        @patient = Patient.find(params[:patient_id])
        uploaded_files = params[:files] || []

        opgs = uploaded_files.map do |file|
          @patient.opgs.create!(
            teeth: JSON.parse(params[:selectedTeeth]),
            justification: params[:justification],
            grade: params[:grade],
            date_taken: params[:date_taken],
            description: params[:description],
            developed_by_id: params[:developed_by],
            taken_by_id: params[:taken_by],
            file: file,
            charting_appointment_id: params[:appointment_id]
          )
        end

        html_content = opgs.map do |opg|
          render_to_string(partial: 'admin/patients/charting/asset_card', locals: { asset: opg })
        end.join

        render json: { success: true, html: html_content }
      end

      def upload_cbct
        @patient = Patient.find(params[:patient_id])
        uploaded_files = params[:files] || []

        cbcts = uploaded_files.map do |file|
          @patient.cbcts.create!(
            teeth: JSON.parse(params[:selectedTeeth]),
            justification: params[:justification],
            grade: params[:grade],
            date_taken: params[:date_taken],
            description: params[:description],
            developed_by_id: params[:developed_by],
            taken_by_id: params[:taken_by],
            file: file,
            charting_appointment_id: params[:appointment_id]
          )
        end

        html_content = cbcts.map do |cbct|
          render_to_string(partial: 'admin/patients/charting/asset_card', locals: { asset: cbct })
        end.join

        render json: { success: true, html: html_content }
      end

      def complete_course_of_treatment
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        if cot.update(completed_at: Time.current)
          render json: { success: true }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def update_cot_name
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        update_params = {}
        update_params[:name] = params[:name] if params[:name].present?
        update_params[:practice_id] = params[:practice_id] if params[:practice_id].present?
        update_params[:lead_clinician_id] = params[:lead_clinician_id] if params[:lead_clinician_id].present?
        update_params[:cot_category_id] = params[:cot_category_id] if params[:cot_category_id].present?

        if cot.update(update_params)
          render json: { success: true }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def load_course_of_treatment_details
        @patient = Patient.find(params[:patient_id])
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        practices = policy_scope(Practice).select(:id, :name)
        clinicians = policy_scope(User).map do |user|
          { name: user.full_name, id: user.id }
        end
        cot_categories = policy_scope(CotCategory).all.map do |tc|
          { name: tc.name, id: tc.id }
        end

        render json: {
          practices: practices.select(:id, :name),
          cot_categories: cot_categories,
          clinicians: clinicians,
          current_cot: {
            id: cot.id,
            name: cot.name,
            practice_id: cot.practice_id,
            lead_clinician_id: cot.lead_clinician_id,
            cot_category_id: cot.cot_category_id
          }
        }
      end

      def accept_cot
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        if cot.update(accepted: true)
          render json: { success: true }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def archive_cot
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        if cot.update(archived: true, archived_at: Time.current)
          render json: {
            success: true,
            redirect_url: admin_patient_charting_path(archived: true)
          }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def activate_cot
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        if cot.update(archived: false, archived_at: nil)
          render json: {
            success: true,
            redirect_url: admin_patient_charting_path(course_of_treatment_id: cot.id)
          }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def copy_cot
        cot = CourseOfTreatment.find(params[:course_of_treatment_id])

        copy_cot = CourseOfTreatments::CopyService.new(cot).call

        if copy_cot
          render json: {
            success: true,
            redirect_url: admin_patient_charting_path(course_of_treatment_id: copy_cot.id)
          }
        else
          render json: { success: false, error: cot.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def assign_patient_treatment
        appointment_id = params[:appointment_id]
        treatment_id = params[:treatment_id]

        appointment = ChartingAppointment.find(appointment_id)

        treatment = Treatment.find(treatment_id)
        index = appointment.charted_treatments.count

        charted_treatment = ChartedTreatment.new(
          charting_appointment: appointment,
          treatment: treatment,
          pricing_type: params[:pricing_type],
          position: 'Patient',
          practitioner: current_user
        )
        if charted_treatment.save
          render partial: 'admin/patients/charting/course_of_treatments/charted_treatment',
                 locals: { charted_treatment: charted_treatment, index: index, is_new: true }, status: :ok
        else
          render json: { success: false, error: charted_treatment.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def find_treatment_by_name
        search_name = params[:name].downcase
        treatments = Treatment.all

        closest_match = treatments.min_by do |treatment|
          Levenshtein.distance(search_name, treatment.patient_friendly_name.downcase)
        end

        if closest_match
          render json: { treatment_code: closest_match.code, matched_name: closest_match.patient_friendly_name }
        else
          render json: { error: 'Treatment not found' }, status: :not_found
        end
      end

      def change_date_and_price
        # TODO: add NHS price here
        @course_of_treatment = CourseOfTreatment.find(params[:course_of_treatment_id])
        if @course_of_treatment.update(charged_date: params[:charged_date])
          render json: { success: true }
        else
          render json: { success: false, error: @course_of_treatment.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def charge_cot
        @course_of_treatment = CourseOfTreatment.find(params[:course_of_treatment_id])
        @charted_treatments =  ChartedTreatment.where(id: params[:ids])

        grouped_charted_treatments = @charted_treatments.group_by(&:practitioner)

        invoices = grouped_charted_treatments.map do |practitioner, group|
          invoice_items = group.map do |charted_treatment|
            InvoiceItem.new(
              name: charted_treatment.treatment.patient_friendly_name,
              charted_treatment_id: charted_treatment.id,
              unit_price: charted_treatment.price,
              quantity: 1
            )
          end

          Invoice.new(
            course_of_treatment: @course_of_treatment,
            invoice_items: invoice_items,
            clinician: practitioner,
            created_by: current_user,
            status: 'unpaid',
            practice: @course_of_treatment.practice,
            patient: @course_of_treatment.patient
          )
        end

        ActiveRecord::Base.transaction do
          invoices.each(&:save!)
        end

        @course_of_treatment.update(charged_date: params[:charged_date])
        render json: { success: true }
      rescue ActiveRecord::RecordInvalid => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def diagnocat
        @patient = Patient.find(params[:patient_id])
      end

      def save_bpe_and_bewe
        @patient = Patient.find(params[:patient_id])

        if params[:bpe].present?
          bpe_scores = [
            params.dig(:bpe, :score1),
            params.dig(:bpe, :score2),
            params.dig(:bpe, :score3),
            params.dig(:bpe, :score4),
            params.dig(:bpe, :score5),
            params.dig(:bpe, :score6)
          ]

          unless bpe_scores.all? { |s| s == '-' }
            @bpe = @patient.bpes.new(bpe_params)
            @bpe.scores = bpe_scores
            @bpe.bleedings = [
              params[:bpe][:bleeding1],
              params[:bpe][:bleeding2],
              params[:bpe][:bleeding3],
              params[:bpe][:bleeding4],
              params[:bpe][:bleeding5],
              params[:bpe][:bleeding6]
            ]
            @bpe.furcations = [
              params[:bpe][:furcation1],
              params[:bpe][:furcation2],
              params[:bpe][:furcation3],
              params[:bpe][:furcation4],
              params[:bpe][:furcation5],
              params[:bpe][:furcation6]
            ]
            @bpe.completed_by = current_user

            if @bpe.save
              flash[:notice] = 'BPE was successfully saved!'
            else
              flash[:alert] = 'BPE was not saved successfully!'
            end
          end
        end

        if params[:bewe].present?
          bewe_scores = [
            params.dig(:bewe, :score1),
            params.dig(:bewe, :score2),
            params.dig(:bewe, :score3),
            params.dig(:bewe, :score4),
            params.dig(:bewe, :score5),
            params.dig(:bewe, :score6)
          ]

          unless bewe_scores.all? { |s| s == '-' }
            @bewe = @patient.bewes.new(bewe_params)
            @bewe.scores = bewe_scores
            @bewe.bleedings = [
              params[:bewe][:bleeding1],
              params[:bewe][:bleeding2],
              params[:bewe][:bleeding3],
              params[:bewe][:bleeding4],
              params[:bewe][:bleeding5],
              params[:bewe][:bleeding6]
            ]
            @bewe.furcations = [
              params[:bewe][:furcation1],
              params[:bewe][:furcation2],
              params[:bewe][:furcation3],
              params[:bewe][:furcation4],
              params[:bewe][:furcation5],
              params[:bewe][:furcation6]
            ]
            @bewe.completed_by = current_user

            if @bewe.save
              flash[:notice] = 'BEWE was successfully saved!'
            else
              flash[:alert] = 'BEWE was not saved successfully!'
            end
          end
        end

        redirect_to admin_patient_charting_path(@patient, bpe_active: true)
      end

      def nhs_medication
        @patient_id = params[:patient_id]
        @medication = Medication.find(params[:id])
      end

      def nhs_medications
        @medications = Medication.where.not(nhs_url: nil)
        @patient_id = params[:patient_id]
      end

      def lock_appointment
        @appointment = ChartingAppointment.find(params[:appointment_id])

        if @appointment.locked?
          render json: { success: false, error: 'Appointment is already locked' }, status: :unprocessable_entity
          return
        end

        ActiveRecord::Base.transaction do
          # Lock the appointment
          @appointment.update!(locked: true, locked_at: Time.current)

          # Mark all charted treatments as completed
          @appointment.charted_treatments.update_all(
            completed: true,
            completed_at: Time.current
          )
        end

        render json: {
          success: true,
          message: 'Appointment locked successfully',
          locked_at: @appointment.locked_at.iso8601
        }
      rescue ActiveRecord::RecordInvalid => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def unlock_appointment
        @appointment = ChartingAppointment.find(params[:appointment_id])

        unless @appointment.locked?
          render json: { success: false, error: 'Appointment is not locked' }, status: :unprocessable_entity
          return
        end

        # Check if 24 hours have passed
        time_since_locked = Time.current - @appointment.locked_at
        if time_since_locked > 24.hours
          render json: { success: false, error: 'Cannot unlock appointment after 24 hours' }, status: :unprocessable_entity
          return
        end

        ActiveRecord::Base.transaction do
          # Unlock the appointment
          @appointment.update!(locked: false, locked_at: nil)
        end

        render json: {
          success: true,
          message: 'Appointment unlocked successfully'
        }
      rescue ActiveRecord::RecordInvalid => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def lock_appointment_note
        @note = AppointmentNote.find(params[:note_id])

        if @note.locked?
          render json: { success: false, error: 'Appointment note is already locked' }, status: :unprocessable_entity
          return
        end

        @note.update!(locked: true, locked_at: Time.current)
        @note.reload # Ensure we have fresh data
        render json: {
          success: true,
          message: 'Appointment note locked successfully',
          locked_at: @note.locked_at.iso8601,
          html: render_to_string(partial: 'admin/patients/charting/course_of_treatments/appointment_note',
                                 locals: { note: @note, appt: @note.charting_appointment })
        }
      rescue StandardError => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def unlock_appointment_note
        @note = AppointmentNote.find(params[:note_id])

        unless @note.locked?
          render json: { success: false, error: 'Appointment note is not locked' }, status: :unprocessable_entity
          return
        end

        # Check if 24 hours have passed
        if @note.locked_at && (Time.current - @note.locked_at) > 24.hours
          render json: { success: false, error: 'Cannot unlock appointment note after 24 hours' }, status: :unprocessable_entity
          return
        end

        @note.update!(locked: false, locked_at: nil)
        @note.reload # Ensure we have fresh data
        render json: {
          success: true,
          message: 'Appointment note unlocked successfully',
          html: render_to_string(partial: 'admin/patients/charting/course_of_treatments/appointment_note',
                                 locals: { note: @note, appt: @note.charting_appointment })
        }
      rescue StandardError => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def load_tooth_history
        @patient = Patient.find(params[:patient_id])
        tooth_position = params[:tooth_position]
        current_course_of_treatment_id = params[:course_of_treatment_id]

        # Get all charted treatments for this tooth across all courses of treatment
        @charted_treatments = ChartedTreatment
                              .joins(charting_appointment: :course_of_treatment)
                              .includes(:treatment, :practitioner, charting_appointment: :course_of_treatment)
                              .where(position: tooth_position)
                              .where.not(treatments: { region: 'Patient' })
                              .where(course_of_treatments: { patient_id: @patient.id })
                              .order(created_at: :desc)

        default_avatar = ActionController::Base.helpers.asset_path('default-avatar.webp')

        # Format the data for the frontend
        treatments_data = @charted_treatments.map do |ct|
          surface =
            case ct.treatment.region
            when 'Tooth', 'Patient'
              nil
            else
              ct.surface_name
            end

          {
            id: ct.id,
            treatment_name: ct.treatment&.patient_friendly_name || 'Unknown Treatment',
            practitioner_name: ct.practitioner&.full_name || 'Unknown Practitioner',
            practitioner_avatar_url: ct.practitioner&.image&.attached? ? ct.practitioner.image&.url : default_avatar,
            course_of_treatment_name: ct.charting_appointment&.course_of_treatment&.name || 'Unknown COT',
            course_of_treatment_id: ct.charting_appointment&.course_of_treatment&.id,
            appointment_date: ct.charting_appointment&.calendar_booking&.start_time&.strftime('%d %b %Y') || 'Unknown Date',
            completed: ct.completed?,
            completed_at: ct.completed_at&.strftime('%d %b %Y'),
            charged: ct.charged?,
            charged_at: ct.charged_at&.strftime('%d %b %Y'),
            notes: ct.notes.presence,
            surface: surface&.humanize,
            price: ct.override_price || 0,
            payment_plan_type: ct.payment_plan_type&.humanize || 'Private',
            urgent: ct.urgent?,
            status_class: ct.completed? ? 'text-green-600' : 'text-yellow-600',
            status_text: ct.completed? ? 'Completed' : 'Planned',
            is_current_cot: ct.charting_appointment&.course_of_treatment_id == current_course_of_treatment_id.to_i
          }
        end

        render json: {
          success: true,
          tooth_position: tooth_position,
          treatments: treatments_data,
          total_treatments: treatments_data.count,
          completed_treatments: treatments_data.count { |t| t[:completed] },
          planned_treatments: treatments_data.count { |t| !t[:completed] }
        }
      rescue StandardError => e
        render json: { success: false, error: e.message }, status: :unprocessable_entity
      end

      def reset_tooth
        tooth_position = params[:tooth_position]&.upcase
        @patient = Patient.find(params[:patient_id])

        if tooth_position.blank?
          render json: { success: false, error: 'Tooth position is required' }, status: :bad_request
          return
        end

        begin
          # Find all charted treatments for this tooth position and patient
          @charted_treatments = ChartedTreatment.includes(charting_appointment: :course_of_treatment)
                                                .where(course_of_treatment: { patient_id: @patient.id })
                                                .where(position: tooth_position)

          deleted_count = @charted_treatments.count

          # Delete all charted treatments for this tooth
          @charted_treatments.destroy_all

          render json: {
            success: true,
            message: "Successfully reset tooth #{tooth_position}",
            deleted_count: deleted_count,
            tooth_position: tooth_position
          }
        rescue StandardError => e
          Rails.logger.error "Error resetting tooth #{tooth_position}: #{e.message}"
          render json: {
            success: false,
            error: 'Failed to reset tooth. Please try again.'
          }, status: :internal_server_error
        end
      end

      def load_calendar_bookings
        @patient = Patient.find(params[:patient_id])

        begin
          # Find future calendar bookings for this patient
          @calendar_bookings = CalendarBooking.where(patient_id: @patient.id)
                                              .joins(:practitioner)
                                              .order(start_time: :desc)
                                              .limit(20)

          # Format the data for the frontend
          bookings_data = @calendar_bookings.map do |booking|
            {
              id: booking.id,
              start_time: booking.start_time.strftime('%A, %d %B %Y at %I:%M %p'),
              practitioner_name: booking.practitioner&.full_name || 'Unknown Practitioner',
              duration: booking.duration_in_minutes,
              status: booking.status,
              notes: booking.notes.presence
            }
          end

          render json: {
            success: true,
            bookings: bookings_data,
            count: bookings_data.length
          }
        rescue StandardError => e
          Rails.logger.error "Error loading calendar bookings: #{e.message}"
          render json: { success: false, error: 'Failed to load calendar bookings' }, status: :internal_server_error
        end
      end

      def associate_calendar_booking
        @patient = Patient.find(params[:patient_id])

        appointment_id = params[:appointment_id]
        calendar_booking_id = params[:calendar_booking_id]

        if appointment_id.blank? || calendar_booking_id.blank?
          render json: { success: false, error: 'Appointment ID and Calendar Booking ID are required' }, status: :bad_request
          return
        end

        begin
          @charting_appointment = ChartingAppointment.find(appointment_id)
          @calendar_booking = CalendarBooking.find(calendar_booking_id)

          # Check if the calendar booking belongs to the same patient
          if @calendar_booking.patient_id != @patient.id
            render json: { success: false, error: 'Calendar booking does not belong to this patient' }, status: :bad_request
            return
          end

          # Associate the calendar booking with the charting appointment
          @charting_appointment.update!(calendar_booking: @calendar_booking)

          render json: {
            success: true,
            message: 'Calendar booking associated successfully',
            appointment_id: appointment_id,
            calendar_booking_id: calendar_booking_id,
            booking_date: @calendar_booking.start_time.strftime('%A, %d %B %Y at %I:%M %p')
          }
        rescue ActiveRecord::RecordNotFound
          render json: { success: false, error: 'Appointment or Calendar Booking not found' }, status: :not_found
        rescue StandardError
          Rails.logger.error "Error associating calendar booking: #{e.message}"
          render json: { success: false, error: 'Failed to associate calendar booking' }, status: :internal_server_error
        end
      end

      def load_all_modals
        @patient = Patient.find(params[:patient_id])

        # Set up instance variables that might be needed by various modals
        @practitioner_users = User.all
        @nurse_users = User.all
        @course_of_treatments = @patient.course_of_treatments
                                        .includes(charting_appointments: :dentist)
                                        .where(archived: false)
                                        .order(created_at: :desc)
        @practice_cot_template_notes = CotTemplateNote.where(practice_id: Current.practice_id || current_user.practice_ids)
        @user_cot_template_notes = CotTemplateNote.where(user_id: current_user.id)
        @medical_histories = @patient.medical_histories.includes(
          :medical_history_questions,
          :medical_history_ai_alerts,
          :medical_history_ai_changes
        ).order(created_at: :desc)
        @available_bookings = CalendarBooking.where(patient: @patient, charting_appointment: nil)
                                             .order(start_date: :desc)
                                             .limit(20)

        render partial: 'admin/patients/charting/modals', locals: { patient: @patient }
      rescue ActiveRecord::RecordNotFound => e
        render json: { error: "Patient not found: #{e.message}" }, status: :not_found
      rescue StandardError => e
        render json: { error: "Failed to load modals: #{e.message}" }, status: :unprocessable_entity
      end

      def favorite_treatment
        treatment = Treatment.find(params[:treatment_id])
        favorite = current_user.user_favorite_treatments.find_by(treatment: treatment)

        if favorite
          favorite.destroy
          favorited = false
        else
          current_user.user_favorite_treatments.create(treatment: treatment)
          favorited = true
        end

        render json: { success: true, favorited: favorited }
      end

      private

      def bpe_params
        params.require(:bpe).permit(:next_assessment, :bpe_tag, scores: [])
      end

      def bewe_params
        params.require(:bewe).permit(:bewe_tag, scores: [])
      end

      def charting_appointment_params
        params.require(:charting_appointment).permit(:course_of_treatment_id, :position)
      end

      def update_details_params
        params.require(:charted_treatment).permit(
          :override_price,
          :duration,
          :completed_at,
          :completed,
          :payment_plan_type,
          :practitioner_id,
          :nurse_id,
          :referrer_id,
          :free_repair,
          :urgent,
          :regulation_11,
          :notes
        )
      end

      def note_params
        params.require(:appointment_note).permit(:content, :user_id, :practitioner_id, :nurse_id, :referrer_id,
                                                 :completed_at).merge(user_id: current_user.id)
      end
    end
  end
end
