# frozen_string_literal: true

module Admin
  class ApplicationController < ApplicationController
    include Pundit::Authorization
    include Devise::Controllers::Helpers

    layout 'admin'

    before_action :authenticate_user!
    before_action :set_site_settings
    before_action :set_current_practice
    before_action :set_user_to_current
    before_action :set_current_ai_attributes
    before_action :unread_messages_count
    before_action :load_notifications
    before_action :load_actions

    before_action do
      ActiveStorage::Current.url_options = { host: request.base_url }
    end

    rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

    def unread_messages_count
      # TODO: extract to separate API call
      @unread_messages_count ||= ConversationMessage.where.not(author_id: current_user.id).unread.count
    end

    def set_site_settings
      @site_settings = SiteSetting.first_or_create
    end

    def set_current_practice
      session[:current_practice_id] = current_user.practices.first.id if current_user.practices.count == 1

      return if session[:current_practice_id].blank?

      Current.practice_id = current_user.practices.find_by(id: session[:current_practice_id])&.id
    end

    def set_user_to_current
      Current.user_id = current_user&.id
    end

    def set_current_ai_attributes
      practice = if Current.practice_id
                   Practice.find(Current.practice_id)
                 else
                   current_user.practices.find { |pr| pr.azure_url.present? && pr.azure_token.present? }
                 end

      return unless practice

      Current.azure_url = practice.azure_url
      Current.azure_token = practice.azure_token
    end

    private

    def user_not_authorized
      flash[:alert] = "You don't have necessary permissions to perform this action."

      redirect_to(request.referer || root_path)
    end

    def load_notifications
      @notifications = current_user.notifications.unread.active.order(created_at: :desc)
    end

    def load_actions
      # Get actions assigned to the current user
      assigned_action_ids = Action.where(deleted_at: nil).where('assigned_to_json::text LIKE ?', "%#{Current.user.id}%").pluck(:id)

      # Only eager load actionable and its patient association to prevent N+1 queries
      # Avoid eager loading created_by and action_comments as recommended
      @actions = Action.includes(actionable: :patient)
                       .where(id: assigned_action_ids)
                       .or(Action.includes(actionable: :patient)
                 .where(created_by_id: current_user.id))

      # Set the notification count for actions due today + no date
      @actions_notification_count = Action.notification_count_for_user(current_user)
    end
  end
end
