# frozen_string_literal: true

module Admin
  class LabDocketsController < Admin::ApplicationController
    def show
      @lab_docket = LabDocket.find(params[:id])
      @lab_work = @lab_docket.lab_work
      @remake_index = @lab_work.lab_dockets.index(@lab_docket) + 1 if @lab_docket
      @lab_items = @lab_work.lab.lab_items.order('name ASC')
      @clinicians = User.clinicians
      @show = true

      render :new
    end

    def new
      @lab_work = LabWork.find(params[:lab_work_id])
      @lab_docket = LabDocket.new
      @remake_index = @lab_work.lab_dockets.count + 1
      @lab_items = @lab_work.lab.lab_items.order('name ASC')
      @clinicians = User.clinicians

      @lab_work.update(code: 6.times.map { rand(10) }.join)
    end

    def edit
      @lab_docket = LabDocket.find(params[:id])
      @lab_work = @lab_docket.lab_work
      @remake_index = @lab_work.lab_dockets.index(@lab_docket) + 1 if @lab_docket
      @lab_items = @lab_work.lab.lab_items.order('name ASC')
      @clinicians = User.clinicians

      render :new
    end

    def create
      @lab_docket = LabDocket.new(lab_docket_params)
      @lab_docket.lab_work_id = params[:lab_docket][:lab_work_id]
      @lab_work = LabWork.find(params[:lab_docket][:lab_work_id])
      @practice = @lab_work.practice

      @lab_docket.draft = true if params[:draft] == '1'
      @lab_docket.docket_info = params[:lab_docket][:docket_info].to_json

      if @lab_docket.save!
        status = params[:draft] == '1' ? 'Draft' : 'Sent To Lab'
        @lab_work.update(status: status)
        redirect_to admin_lab_works_path
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      @lab_docket = LabDocket.find(params[:id])
      @lab_work = @lab_docket.lab_work
      @practice = @lab_work.practice

      @lab_docket.docket_info = params[:lab_docket][:docket_info].to_json

      if @lab_docket.update(lab_docket_params)
        @lab_work.update(status: 'Sent To Lab') if @lab_work.status == 'Draft'
        redirect_to admin_lab_work_path(@lab_work)
      else
        render :new, status: :unprocessable_entity
      end
    end

    def create_pdf
      @lab_docket = LabDocket.find(params[:id])
      @lab_docket_items = @lab_docket.lab_docket_items
      @lab_work = @lab_docket.lab_work
      @lab = @lab_work.lab

      @total = @lab_docket_items.map(&:lab_docket_treatments).flatten.map do |item|
        li = item.lab_item
        li.price.to_f * (item.units || 1).to_f
      end.sum

      respond_to do |format|
        format.html do
          render pdf: Time.now.to_i.to_s, template: 'admin/lab_dockets/pdf'
        end
      end
    end

    private

    def lab_docket_params
      params.require(:lab_docket).permit(
        :id,
        :lab_work_id,
        :interoral_scan,
        :photos_sent,
        :physical_impressions_taken,
        :instructions,
        :disinfected_with,
        :disinfected_by_id,
        :made_available,
        :disinfectant_further_information,
        docket_info: %i[job_number_for_lab_use date_in date_return],
        lab_docket_items_attributes: [
          :units,
          :_destroy,
          :id,
          {
            info: {},
            lab_docket_treatments_attributes: %i[
              lab_item_id
              units
              further_information
              treatment_type
              arch
              materials
              post_core
              num_wings
              stage
              post_core_materials
              implant_system
              abutment_material
              margin
              retention
              _destroy
            ]
          }
        ]
      )
    end
  end
end
