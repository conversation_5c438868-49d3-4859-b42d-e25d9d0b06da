# frozen_string_literal: true

module Admin
  module GeneralSettings
    class CotTemplateNotesController < Admin::ApplicationController
      before_action :set_cot_template_note, except: %i[index new create]

      def index
        @practice_id = Current.practice_id || current_user.practice_ids.first
        @user_id = Current.user_id
        @practice_cot_template_notes = CotTemplateNote.where(practice_id: Current.practice_id || current_user.practice_ids)
        @user_cot_template_notes = CotTemplateNote.where(user_id: current_user.id)
      end

      def new
        @cot_template_note = CotTemplateNote.new(practice_id: params[:practice_id], user_id: params[:user_id])
        @user = current_user
        @practices = Practice.all
      end

      def edit
        @user = current_user
        @practices = Practice.all
        @current_selected_user = @cot_template_note.user if @cot_template_note.user_id.present?
      end

      def create
        @cot_template_note = CotTemplateNote.new(cot_template_note_params)

        if @cot_template_note.save
          redirect_to admin_general_settings_cot_template_notes_path, notice: 'COT Template Note added!'
        else
          flash[:alert] = "Error creating COT Template Note: #{@cot_template_note.errors.full_messages.join(', ')}"
          @user = current_user
          @practices = Practice.all
          render :new
        end
      end

      def update
        @user = current_user
        @practices = Practice.all
        if @cot_template_note.update(cot_template_note_params)
          redirect_to admin_general_settings_cot_template_notes_path, notice: 'COT Template Note updated!'
        else
          flash[:alert] = "Error updating COT Template Note: #{@cot_template_note.errors.full_messages.join(', ')}"
          render :edit
        end
      end

      def destroy
        if @cot_template_note.destroy
          redirect_to admin_general_settings_cot_template_notes_path,
                      notice: 'COT Template Note was successfully deleted.'
        else
          redirect_to admin_general_settings_cot_template_notes_path,
                      alert: 'Failed to delete COT Template Note.'
        end
      end

      def convert_to_user_note
        if @cot_template_note.update(practice_id: nil, user_id: current_user.id)
          redirect_to admin_general_settings_cot_template_notes_path, notice: 'COT Template Note converted!'
        else
          redirect_to admin_general_settings_cot_template_notes_path, error: 'COT Template Note was not converted.'
        end
      end

      def convert_to_practice_note
        if @cot_template_note.update(practice_id: Current.practice_id || current_user.practice_ids.first, user_id: nil)
          redirect_to admin_general_settings_cot_template_notes_path, notice: 'COT Template Note converted!'
        else
          redirect_to admin_general_settings_cot_template_notes_path, error: 'COT Template Note was not converted.'
        end
      end

      private

      def set_cot_template_note
        @cot_template_note = CotTemplateNote.find(params[:id])
      end

      def cot_template_note_params
        params.require(:cot_template_note).permit(:title, :content, :practice_id, :user_id)
      end
    end
  end
end
